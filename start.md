I'll help you understand how to start both the backend and frontend components of the biormika application. Let me examine the codebase structure and configuration files to provide you with detailed startup instructions.

Let me check if there are any hidden files or directories that might contain the frontend/backend structure:

Great! I can see there are separate `backend` and `frontend` directories. Let me examine both directories to understand their structure and configuration:

Now let me examine the key configuration files to understand the dependencies and startup scripts:

Let me check for any README files or documentation:

Let me check if there are any root-level documentation files:

Let me also check if there's a virtual environment already set up in the backend:

Perfect! Now let me check if the frontend dependencies are already installed:

Let me check what Python version is being used and if the backend dependencies are installed:

Based on my examination of the biormika application codebase, here are the detailed startup instructions:

## Project Structure
- **Backend**: FastAPI application with Python virtual environment
- **Frontend**: Next.js application with React and TypeScript
- **Purpose**: HFO (High-Frequency Oscillations) detection in EEG data

## Prerequisites
- Python 3.12+ (already set up with virtual environment)
- Node.js and npm (for frontend)
- Both backend and frontend dependencies appear to be already installed

## Startup Instructions

### 1. Start the Backend Server

```bash
# Navigate to the backend directory
cd backend

# Activate the Python virtual environment
source venv/bin/activate

# Install dependencies (if not already installed)
pip install -r requirements.txt

# Start the FastAPI server with auto-reload
uvicorn app:app --reload --port 8000
```

**Backend will be available at:** `http://localhost:8000`
- API documentation: `http://localhost:8000/docs`
- WebSocket endpoint: `ws://localhost:8000/ws`

### 2. Start the Frontend Development Server

Open a **new terminal window/tab** and run:

```bash
# Navigate to the frontend directory
cd frontend

# Install dependencies (if not already installed)
npm install

# Start the Next.js development server with Turbopack
npm run dev
```

**Frontend will be available at:** `http://localhost:3000`

## Development vs Production

### Development Mode (Recommended for local development)
- **Backend**: `uvicorn app:app --reload --port 8000` (auto-reloads on code changes)
- **Frontend**: `npm run dev` (hot reload enabled with Turbopack)

### Production Mode
```bash
# Backend (from backend directory)
uvicorn app:app --host 0.0.0.0 --port 8000

# Frontend (from frontend directory)
npm run build
npm start
```

## Important Configuration Notes

1. **Ports**:
   - Backend: `8000`
   - Frontend: `3000`
   - CORS is configured for localhost:3000 and localhost:3002

2. **WebSocket Communication**: The frontend connects to the backend via WebSocket for real-time EEG data streaming and HFO detection results.

3. **File Upload**: The application processes EDF (European Data Format) files for EEG analysis.

4. **Dependencies Already Installed**: Both the Python virtual environment and Node.js modules appear to be already set up.

## Quick Start Commands (Copy & Paste)

**Terminal 1 (Backend):**
```bash
cd /Users/<USER>/Work/biormika/biormika-http-backend/backend
source venv/bin/activate
uvicorn app:app --reload --port 8000
```

**Terminal 2 (Frontend):**
```bash
cd /Users/<USER>/Work/biormika/biormika-http-backend/frontend
npm run dev
```

Once both servers are running, open `http://localhost:3000` in your browser to access the application. The frontend will automatically connect to the backend API and WebSocket endpoints.
