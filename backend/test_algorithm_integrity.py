"""
Test file to verify HFO algorithm integrity during refactoring.
This captures the current behavior to ensure no changes to the core algorithm.
"""

import numpy as np
import json
import hashlib
from datetime import datetime
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'core', 'hfo_engine'))
from hfo_analysis import run_hfo_algorithm

class AlgorithmIntegrityTester:
    """Test suite to ensure HFO algorithm produces identical results"""
    
    def __init__(self):
        self.baseline_results = None
        self.test_parameters = {
            'threshold_option1': 2,  # Amplitude 1
            'threshold_option2': 2,  # Amplitude 2
            'threshold_option3': 6,  # Peaks 1
            'threshold_option4': 3,  # Peaks 2
            'threshold_option5': 10, # Duration (ms)
            'threshold_option6': 10, # Temporal sync (ms)
            'threshold_option7': 10, # Spatial sync (ms)
            'locutoff': 50,
            'hicutoff': 300,
            'montage': 'Bipolar montage',
            'user_ref': None
        }
    
    def create_test_eeg_data(self):
        """Create synthetic EEG data with known patterns"""
        # Create a simple test EEG structure
        srate = 1000  # 1000 Hz sampling rate
        duration = 10  # 10 seconds
        n_channels = 4
        n_samples = srate * duration
        
        # Create synthetic EEG data with some HFO-like patterns
        time = np.linspace(0, duration, n_samples)
        eeg_data = np.zeros((n_channels, n_samples))
        
        # Add base signal
        for ch in range(n_channels):
            # Background EEG (low frequency)
            eeg_data[ch] = 10 * np.sin(2 * np.pi * 10 * time + ch * np.pi/4)
            
            # Add some HFO-like bursts (high frequency oscillations)
            hfo_times = [2.0, 5.0, 8.0]  # HFO at 2, 5, and 8 seconds
            for hfo_time in hfo_times:
                hfo_start = int(hfo_time * srate)
                hfo_duration = int(0.05 * srate)  # 50ms HFO
                if hfo_start + hfo_duration < n_samples:
                    # Add 200 Hz oscillation
                    hfo_signal = 5 * np.sin(2 * np.pi * 200 * time[hfo_start:hfo_start+hfo_duration])
                    eeg_data[ch, hfo_start:hfo_start+hfo_duration] += hfo_signal
        
        # Create EEG structure as expected by the algorithm
        eeg_struct = {
            'data': eeg_data,
            'nbchan': n_channels,
            'srate': srate,
            'pnts': n_samples,
            'times': time,
            'chanlocs': [f'CH{i+1}' for i in range(n_channels)],
            'setname': 'test_eeg',
            'comments': 'Synthetic test data',
            'xmin': 0,
            'xmax': duration
        }
        
        return eeg_struct
    
    def run_baseline_test(self, eeg_data=None):
        """Run algorithm and capture baseline results"""
        if eeg_data is None:
            eeg_data = self.create_test_eeg_data()
        
        # Run the algorithm
        result = run_hfo_algorithm(
            EEG=eeg_data,
            input_file_path='test_file.edf',
            analysis_start=0,
            analysis_end=10,
            montage=self.test_parameters['montage'],
            user_ref=self.test_parameters['user_ref'],
            locutoff=self.test_parameters['locutoff'],
            hicutoff=self.test_parameters['hicutoff'],
            gui_output=None,
            threshold_option1=self.test_parameters['threshold_option1'],
            threshold_option2=self.test_parameters['threshold_option2'],
            threshold_option3=self.test_parameters['threshold_option3'],
            threshold_option4=self.test_parameters['threshold_option4'],
            threshold_option5=self.test_parameters['threshold_option5'],
            threshold_option6=self.test_parameters['threshold_option6'],
            threshold_option7=self.test_parameters['threshold_option7']
        )
        
        self.baseline_results = result
        return result
    
    def verify_algorithm_integrity(self, new_result):
        """Compare new results with baseline to ensure identical behavior"""
        if self.baseline_results is None:
            raise ValueError("No baseline results. Run run_baseline_test first.")
        
        # Compare key metrics
        checks = {
            'success_status': new_result.get('success') == self.baseline_results.get('success'),
            'hfo_count': self._compare_hfo_counts(new_result, self.baseline_results),
            'hfo_timings': self._compare_hfo_timings(new_result, self.baseline_results),
            'hfo_characteristics': self._compare_hfo_characteristics(new_result, self.baseline_results)
        }
        
        all_passed = all(checks.values())
        
        return {
            'passed': all_passed,
            'checks': checks,
            'details': self._get_comparison_details(new_result, self.baseline_results)
        }
    
    def _compare_hfo_counts(self, result1, result2):
        """Compare number of HFOs detected"""
        count1 = self._extract_hfo_count(result1)
        count2 = self._extract_hfo_count(result2)
        return count1 == count2
    
    def _compare_hfo_timings(self, result1, result2, tolerance=1e-6):
        """Compare HFO timing with tolerance for floating point"""
        timings1 = self._extract_hfo_timings(result1)
        timings2 = self._extract_hfo_timings(result2)
        
        if len(timings1) != len(timings2):
            return False
        
        for t1, t2 in zip(timings1, timings2):
            if abs(t1 - t2) > tolerance:
                return False
        return True
    
    def _compare_hfo_characteristics(self, result1, result2, tolerance=1e-6):
        """Compare HFO characteristics (frequency, amplitude, etc.)"""
        chars1 = self._extract_hfo_characteristics(result1)
        chars2 = self._extract_hfo_characteristics(result2)
        
        if len(chars1) != len(chars2):
            return False
        
        # Compare each characteristic
        for c1, c2 in zip(chars1, chars2):
            for key in c1:
                if key in c2:
                    if isinstance(c1[key], (int, float)):
                        if abs(c1[key] - c2[key]) > tolerance:
                            return False
                    elif c1[key] != c2[key]:
                        return False
        return True
    
    def _extract_hfo_count(self, result):
        """Extract HFO count from result"""
        if result.get('success') and 'hfos' in result:
            return len(result['hfos'])
        return 0
    
    def _extract_hfo_timings(self, result):
        """Extract HFO timings from result"""
        if result.get('success') and 'hfos' in result:
            return [hfo.get('start_time', 0) for hfo in result['hfos']]
        return []
    
    def _extract_hfo_characteristics(self, result):
        """Extract HFO characteristics from result"""
        if result.get('success') and 'hfos' in result:
            return [
                {
                    'duration': hfo.get('duration', 0),
                    'frequency': hfo.get('frequency', 0),
                    'amplitude': hfo.get('amplitude', 0),
                    'channel': hfo.get('channel', '')
                }
                for hfo in result['hfos']
            ]
        return []
    
    def _get_comparison_details(self, result1, result2):
        """Get detailed comparison information"""
        return {
            'baseline_hfo_count': self._extract_hfo_count(result2),
            'new_hfo_count': self._extract_hfo_count(result1),
            'baseline_success': result2.get('success'),
            'new_success': result1.get('success')
        }
    
    def save_baseline(self, filename='baseline_results.json'):
        """Save baseline results to file"""
        if self.baseline_results:
            # Convert numpy arrays to lists for JSON serialization
            serializable_results = self._make_serializable(self.baseline_results)
            with open(filename, 'w') as f:
                json.dump({
                    'timestamp': datetime.now().isoformat(),
                    'parameters': self.test_parameters,
                    'results': serializable_results
                }, f, indent=2)
    
    def load_baseline(self, filename='baseline_results.json'):
        """Load baseline results from file"""
        with open(filename, 'r') as f:
            data = json.load(f)
            self.baseline_results = data['results']
            self.test_parameters = data['parameters']
    
    def _make_serializable(self, obj):
        """Convert numpy arrays and other non-serializable objects"""
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        else:
            return obj

def main():
    """Run integrity tests"""
    print("HFO Algorithm Integrity Tester")
    print("=" * 50)
    
    tester = AlgorithmIntegrityTester()
    
    print("Creating test EEG data...")
    test_eeg = tester.create_test_eeg_data()
    
    print("Running baseline algorithm...")
    try:
        baseline = tester.run_baseline_test(test_eeg)
        print(f"Baseline test completed: Success={baseline.get('success')}")
        
        # Save baseline for future comparison
        tester.save_baseline()
        print("Baseline results saved to baseline_results.json")
        
    except Exception as e:
        print(f"Error running baseline test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()