from fastapi import FastAPI
import sys
import logging

sys.path.append('./core/hfo_engine')

from api.middleware import setup_middleware
from api.routes import parameters, analysis, websocket

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="Biormika HFO Detector MVP", version="1.0.0")

setup_middleware(app)

app.include_router(parameters.router)
app.include_router(analysis.router)
app.include_router(websocket.router)


@app.get("/")
async def root():
    return {"message": "Biormika HFO Detector MVP", "version": "1.0.0"}