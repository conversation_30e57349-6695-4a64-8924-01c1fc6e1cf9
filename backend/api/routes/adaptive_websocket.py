from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Query
import asyncio
import logging
import traceback

from api import dependencies as deps
from api.routes.analysis import _extract_channel_groups
from core.utils import SamplingRateUtils
from models.parameters import AnalysisParameters

logger = logging.getLogger(__name__)
router = APIRouter()


@router.websocket("/ws/adaptive")
async def adaptive_websocket_endpoint(
    websocket: WebSocket, 
    chunked: bool = Query(default=None, description="Use chunked processing mode")
):
    """
    Adaptive WebSocket endpoint that can use either regular or chunked processing.
    
    Query parameters:
        chunked: If true, use chunked processing. If false, use regular.
                If not specified, uses server default (USE_CHUNKED_MODE).
    """
    
    await websocket.accept()
    deps.active_websocket = websocket
    processor = None
    
    # Determine processing mode
    use_chunked = chunked if chunked is not None else deps.USE_CHUNKED_MODE
    
    try:
        if not deps.current_filepath:
            await websocket.send_json({
                "type": "error",
                "message": "No file selected. Please call /api/analyze first."
            })
            return
        
        await websocket.send_json({
            "type": "status",
            "message": f"Starting {'chunked' if use_chunked else 'regular'} analysis...",
            "mode": "chunked" if use_chunked else "regular"
        })
        
        # Get parameters
        if deps.current_parameters is not None:
            parameters = deps.current_parameters
        else:
            default_params = {
                "thresholds": {
                    "amplitude1": 2,
                    "amplitude2": 2,
                    "peaks1": 6,
                    "peaks2": 3,
                    "duration": 10,
                    "temporal_sync": 10,
                    "spatial_sync": 10
                },
                "montage": {"type": "bipolar"},
                "frequency": {"low_cutoff": 50, "high_cutoff": 300},
                "time_segment": {"mode": "entire_file"},
                "channel_selection": {"selected_leads": [], "contact_specifications": {}}
            }
            parameters = AnalysisParameters(**default_params)
        
        # Validate parameters
        from browse_files import extract_edf_metadata
        header = extract_edf_metadata(deps.current_filepath)
        
        sampling_rate = SamplingRateUtils.extract_sampling_rate(header['frequency'])
        channel_groups = _extract_channel_groups(header['label'])
        available_leads = list({*channel_groups.keys(), *header['label']})

        validation_data = {
            "thresholds": parameters.thresholds.model_dump(),
            "frequency": parameters.frequency.model_dump(),
            "montage": parameters.montage.model_dump(),
            "time_segment": parameters.time_segment.model_dump(),
            "channel_selection": parameters.channel_selection.model_dump(),
            "available_channels": header['label'],
            "lead_groups": available_leads,
            "sampling_rate": sampling_rate,
            "file_duration": header['records'] * header['duration']
        }
        
        is_valid, param_errors = deps.parameter_validator.validate(validation_data)
        if not is_valid:
            await websocket.send_json({
                "type": "validation_error",
                "message": "Invalid analysis parameters",
                "errors": param_errors
            })
            return
        
        if deps.parameter_validator.warnings:
            await websocket.send_json({
                "type": "validation_warning",
                "warnings": deps.parameter_validator.warnings
            })
        
        if use_chunked:
            # Use chunked processing
            processor = await deps.chunked_analysis_service.create_processor(
                deps.current_filepath, 
                parameters,
                chunk_duration=10.0
            )
            
            # Send initialization info
            await websocket.send_json({
                "type": "initialization_complete",
                "data": {
                    "total_chunks": processor.get_total_chunks(),
                    "chunk_duration": processor.chunk_duration,
                    "sampling_rate": processor.sampling_rate,
                    "mode": "chunked"
                }
            })
            
            # Process preview
            preview_result = await processor.process_preview()
            if not preview_result.get("error"):
                await websocket.send_json({
                    "type": "preview",
                    "data": preview_result
                })
            
            # Callback for progressive HFO results
            async def send_hfo_batch(hfo_data):
                await websocket.send_json({
                    "type": "hfo_batch",
                    "data": hfo_data
                })
            
            total_chunks = processor.get_total_chunks()
            
            # Process chunks
            for chunk_num in range(total_chunks):
                chunk_result = await processor.process_chunk(chunk_num, send_hfo_batch)
                
                progress = ((chunk_num + 1) / total_chunks) * 100
                
                if chunk_result.get("channel_data"):
                    await websocket.send_json({
                        "type": "chunk",
                        "data": {
                            "chunk_number": chunk_num,
                            "total_chunks": total_chunks,
                            "time_range": chunk_result["time_range"],
                            "hfo_events": chunk_result.get("hfo_events", []),
                            "channel_data": chunk_result.get("channel_data", {}),
                            "progress": progress,
                            "hfo_count": chunk_result.get("hfo_count", 0)
                        }
                    })
                
                await asyncio.sleep(0.01)
                
        else:
            # Use regular processing
            processor = await deps.analysis_service.create_processor(
                deps.current_filepath, 
                parameters
            )
            
            # Send preview
            preview_result = await processor.process_preview()
            if not preview_result.get("error"):
                await websocket.send_json({
                    "type": "preview",
                    "data": preview_result
                })
            
            total_chunks = processor.get_total_chunks()
            
            # Process chunks (regular mode)
            for chunk_num in range(total_chunks):
                chunk_result = await processor.process_chunk(chunk_num)
                
                progress = ((chunk_num + 1) / total_chunks) * 100
                
                if chunk_result.get("channel_data"):
                    await websocket.send_json({
                        "type": "chunk",
                        "data": {
                            "chunk_number": chunk_num,
                            "total_chunks": total_chunks,
                            "time_range": chunk_result["time_range"],
                            "hfo_events": chunk_result.get("hfo_events", []),
                            "channel_data": chunk_result.get("channel_data", {}),
                            "progress": progress
                        }
                    })
                
                await asyncio.sleep(0.01)
        
        # Send completion
        await websocket.send_json({
            "type": "complete",
            "data": {
                "total_hfos": processor.get_total_hfos(),
                "summary": processor.get_summary(),
                "mode": "chunked" if use_chunked else "regular"
            }
        })
        
    except WebSocketDisconnect:
        deps.active_websocket = None
    except Exception as e:
        await websocket.send_json({
            "type": "error",
            "message": str(e),
            "traceback": traceback.format_exc()
        })
        deps.active_websocket = None
    finally:
        if processor and hasattr(processor, 'cleanup'):
            processor.cleanup()
