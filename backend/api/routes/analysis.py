from fastapi import APIRouter, HTTPException
import logging
from typing import Dict, List

from api.models import FileAnalyzeRequest, StartAnalysisRequest
from api import dependencies as deps
from core.utils import SamplingRateUtils, ChannelLabelUtils
from core.exceptions.validation_exceptions import ValidationError
from models.parameters import AnalysisParameters

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api", tags=["analysis"])


def _extract_channel_groups(labels: List[str]) -> Dict[str, List[int]]:
    """Extract channel groups from labels (e.g., 'FP1', 'FP2' -> group 'FP' with contacts [1,2])"""
    return ChannelLabelUtils.extract_channel_groups(labels)


@router.post("/analyze")
async def analyze(request: FileAnalyzeRequest):
    """Start analysis of a local EDF file with comprehensive validation"""
    
    try:
        is_valid, errors = deps.edf_validator.validate_file(request.filepath)
        if not is_valid:
            logger.error(f"EDF file validation failed: {errors}")
            raise ValidationError(
                message="EDF file validation failed",
                errors=errors,
                field="filepath"
            )
        
        from browse_files import extract_edf_metadata
        try:
            header = extract_edf_metadata(request.filepath)
            is_valid, errors = deps.edf_validator.validate_header(header)
            if not is_valid:
                logger.error(f"EDF header validation failed: {errors}")
                raise ValidationError(
                    message="EDF header validation failed",
                    errors=errors
                )
        except Exception as e:
            logger.error(f"Failed to read EDF file: {str(e)}")
            raise ValidationError(
                message="Failed to read EDF file",
                errors=[str(e)]
            )
        
        deps.current_filepath = request.filepath
        
        sampling_rate = SamplingRateUtils.extract_sampling_rate(header['frequency'])
        
        return {
            "status": "ready",
            "message": "File validated successfully. Connect to WebSocket to start streaming.",
            "filepath": request.filepath,
            "file_info": {
                "channels": header['label'],
                "sampling_rate": sampling_rate,
                "duration_seconds": header['records'] * header['duration'],
                "start_date": header['startdate'],
                "start_time": header['starttime']
            },
            "validation_warnings": deps.edf_validator.warnings if deps.edf_validator.warnings else None
        }
        
    except ValidationError:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in analyze endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/analyze/start")
async def start_analysis(request: StartAnalysisRequest):
    """Start analysis with full parameter configuration"""
    
    try:
        is_valid, errors = deps.edf_validator.validate_file(request.filepath)
        if not is_valid:
            raise ValidationError(
                message="EDF file validation failed",
                errors=errors,
                field="filepath"
            )
        
        from browse_files import extract_edf_metadata
        header = extract_edf_metadata(request.filepath)
        
        is_valid, errors = deps.edf_validator.validate_header(header)
        if not is_valid:
            raise ValidationError(
                message="EDF header validation failed",
                errors=errors
            )
        
        sampling_rate = SamplingRateUtils.extract_sampling_rate(header['frequency'])
        
        if request.parameters:
            params = request.parameters
        else:
            params = {
                "thresholds": {},
                "montage": {"type": "bipolar"},
                "frequency": {"low_cutoff": 50, "high_cutoff": 300},
                "time_segment": {"mode": "entire_file"},
                "channel_selection": {"selected_leads": [], "contact_specifications": {}}
            }
        
        if params.get("frequency"):
            high_cutoff = params["frequency"].get("high_cutoff", 300)
            max_freq = sampling_rate / 3
            
            if high_cutoff > max_freq:
                raise ValidationError(
                    message=f"High cutoff frequency ({high_cutoff}Hz) exceeds maximum usable frequency ({max_freq:.1f}Hz) for sampling rate {sampling_rate}Hz",
                    errors=[f"Maximum frequency should be sampling_rate/3 = {max_freq:.1f}Hz"],
                    field="frequency.high_cutoff"
                )
        
        if params.get("time_segment"):
            time_seg = params["time_segment"]
            file_duration = header['records'] * header['duration']
            
            if time_seg.get("mode") == "start_end_times":
                if not all([time_seg.get("start_date"), time_seg.get("start_time"),
                           time_seg.get("end_date"), time_seg.get("end_time")]):
                    raise ValidationError(
                        message="Start/end times mode requires all date/time fields",
                        errors=["Missing required date/time fields"],
                        field="time_segment"
                    )
            elif time_seg.get("mode") == "start_time_duration":
                if not all([time_seg.get("start_date"), time_seg.get("start_time"),
                           time_seg.get("duration_seconds")]):
                    raise ValidationError(
                        message="Start time/duration mode requires start date/time and duration",
                        errors=["Missing required fields"],
                        field="time_segment"
                    )
                if time_seg.get("duration_seconds", 0) > file_duration:
                    raise ValidationError(
                        message=f"Duration ({time_seg['duration_seconds']}s) exceeds file duration ({file_duration}s)",
                        errors=["Duration too long"],
                        field="time_segment.duration_seconds"
                    )
        
        if params.get("montage"):
            montage = params["montage"]
            if montage.get("type") == "referential" and not montage.get("reference_channel"):
                raise ValidationError(
                    message="Referential montage requires a reference channel",
                    errors=["Missing reference channel"],
                    field="montage.reference_channel"
                )
            
            if montage.get("type") == "bipolar" and len(header['label']) < 2:
                raise ValidationError(
                    message="Bipolar montage requires at least 2 channels",
                    errors=[f"Only {len(header['label'])} channel(s) available"],
                    field="montage.type"
                )
        
        deps.current_filepath = request.filepath
        # Persist parameters for the websocket session to use
        try:
            deps.current_parameters = AnalysisParameters(**params)  # type: ignore[arg-type]
        except Exception:
            # Fall back to None if parameters fail to parse; WS will use defaults
            deps.current_parameters = None
        
        channel_groups = _extract_channel_groups(header['label'])
        
        return {
            "status": "ready",
            "message": "Analysis parameters validated. Ready to start streaming.",
            "filepath": request.filepath,
            "file_info": {
                "channels": header['label'],
                "channel_groups": channel_groups,
                "sampling_rate": sampling_rate,
                "max_frequency": sampling_rate / 3,
                "duration_seconds": header['records'] * header['duration'],
                "start_date": header['startdate'],
                "start_time": header['starttime']
            },
            "validated_parameters": params
        }
        
    except ValidationError:
        raise
    except Exception as e:
        logger.error(f"Error in start_analysis: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))