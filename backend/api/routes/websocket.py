from fastapi import APIRouter, WebSocket, WebSocketDisconnect
import asyncio
import logging
import traceback

from api import dependencies as deps
from api.routes.analysis import _extract_channel_groups
from core.utils import SamplingRateUtils
from models.parameters import AnalysisParameters

logger = logging.getLogger(__name__)
router = APIRouter()


@router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time EEG data streaming with chunked processing"""

    await websocket.accept()
    deps.active_websocket = websocket
    processor = None

    try:
        if not deps.current_filepath:
            await websocket.send_json({
                "type": "error",
                "message": "No file selected. Please call /api/analyze first."
            })
            return
        
        await websocket.send_json({
            "type": "status",
            "message": "Starting analysis..."
        })
        
        # Use parameters from the /analyze/start request if available; otherwise defaults
        if deps.current_parameters is not None:
            parameters = deps.current_parameters
        else:
            default_params = {
                "thresholds": {
                    "amplitude1": 2,
                    "amplitude2": 2,
                    "peaks1": 6,
                    "peaks2": 3,
                    "duration": 10,
                    "temporal_sync": 10,
                    "spatial_sync": 10
                },
                "montage": {"type": "bipolar"},
                "frequency": {"low_cutoff": 50, "high_cutoff": 300},
                "time_segment": {"mode": "entire_file"},
                "channel_selection": {"selected_leads": [], "contact_specifications": {}}
            }
            parameters = AnalysisParameters(**default_params)
        
        from browse_files import extract_edf_metadata
        header = extract_edf_metadata(deps.current_filepath)
        
        sampling_rate = SamplingRateUtils.extract_sampling_rate(header['frequency'])
        
        channel_groups = _extract_channel_groups(header['label'])
        # Allow validation against both group names and exact labels
        available_leads = list({*channel_groups.keys(), *header['label']})

        validation_data = {
            "thresholds": parameters.thresholds.model_dump(),
            "frequency": parameters.frequency.model_dump(),
            "montage": parameters.montage.model_dump(),
            "time_segment": parameters.time_segment.model_dump(),
            "channel_selection": parameters.channel_selection.model_dump(),
            "available_channels": header['label'],
            "lead_groups": available_leads,
            "sampling_rate": sampling_rate,
            "file_duration": header['records'] * header['duration']
        }
        
        is_valid, param_errors = deps.parameter_validator.validate(validation_data)
        if not is_valid:
            await websocket.send_json({
                "type": "validation_error",
                "message": "Invalid analysis parameters",
                "errors": param_errors
            })
            logger.error(f"Parameter validation failed: {param_errors}")
            return
        
        if deps.parameter_validator.warnings:
            await websocket.send_json({
                "type": "validation_warning",
                "warnings": deps.parameter_validator.warnings
            })
        
        # Create processor with chunked processing as default (memory optimization)
        processor = await deps.analysis_service.create_processor(
            deps.current_filepath,
            parameters,
            chunk_duration=10.0
        )

        # Send initialization complete with chunked processing info
        if hasattr(processor, 'chunk_duration'):
            await websocket.send_json({
                "type": "initialization_complete",
                "data": {
                    "total_chunks": processor.get_total_chunks(),
                    "chunk_duration": processor.chunk_duration,
                    "sampling_rate": processor.sampling_rate,
                    "mode": "chunked"
                }
            })

        preview_result = await processor.process_preview()
        if not preview_result.get("error"):
            await websocket.send_json({
                "type": "preview",
                "data": preview_result
            })

        # Define callback for progressive HFO results (if using chunked processor)
        async def send_hfo_batch(hfo_data):
            """Send HFO results as they're detected"""
            await websocket.send_json({
                "type": "hfo_batch",
                "data": hfo_data
            })

        total_chunks = processor.get_total_chunks()

        for chunk_num in range(total_chunks):
            # Use progressive HFO detection if supported (chunked processor)
            if hasattr(processor, 'chunk_duration'):
                chunk_result = await processor.process_chunk(chunk_num, send_hfo_batch)
            else:
                # Fallback for legacy processor
                chunk_result = await processor.process_chunk(chunk_num)

            progress = ((chunk_num + 1) / total_chunks) * 100

            # Only send chunks that have channel data to render
            if chunk_result.get("channel_data"):
                await websocket.send_json({
                    "type": "chunk",
                    "data": {
                        "chunk_number": chunk_num,
                        "total_chunks": total_chunks,
                        "time_range": chunk_result["time_range"],
                        "hfo_events": chunk_result.get("hfo_events", []),
                        "channel_data": chunk_result.get("channel_data", {}),
                        "progress": progress,
                        "hfo_count": chunk_result.get("hfo_count", 0)
                    }
                })

            await asyncio.sleep(0.01)
        
        await websocket.send_json({
            "type": "complete",
            "data": {
                "total_hfos": processor.get_total_hfos(),
                "summary": processor.get_summary()
            }
        })
        
    except WebSocketDisconnect:
        deps.active_websocket = None
    except Exception as e:
        await websocket.send_json({
            "type": "error",
            "message": str(e),
            "traceback": traceback.format_exc()
        })
        deps.active_websocket = None
    finally:
        # Clean up resources
        if processor and hasattr(processor, 'cleanup'):
            processor.cleanup()