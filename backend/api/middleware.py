from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from core.exceptions.error_handlers import setup_exception_handlers


def setup_middleware(app: FastAPI):
    setup_exception_handlers(app)
    
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["http://localhost:3000", "http://localhost:3001", "http://localhost:3002"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )