import os
from typing import Optional
from fastapi import WebSocket

from core.validators import EDFValidator, ParameterValidator, SignalValidator
from models.parameters import AnalysisParameters
from services.analysis_service import AnalysisService

# Global state management
active_websocket: Optional[WebSocket] = None
current_filepath: Optional[str] = None
current_parameters: Optional[AnalysisParameters] = None

# Service instances
analysis_service = AnalysisService()

# Validator instances
edf_validator = EDFValidator()
parameter_validator = ParameterValidator()
signal_validator = SignalValidator()

# Create uploads directory
UPLOAD_DIR = "uploads"
os.makedirs(UPLOAD_DIR, exist_ok=True)