import numpy as np
import re
from typing import Iterator, Dict, Any, List, Optional, Tuple


class ChunkedEDFReader:
    """EDF file reader that supports chunked/streaming data loading"""
    
    def __init__(self, filename: str, chunk_duration: float = 10.0):
        """
        Initialize chunked EDF reader
        
        Args:
            filename: Path to EDF file
            chunk_duration: Duration of each chunk in seconds (default: 10s)
        """
        self.filename = filename
        self.chunk_duration = chunk_duration
        self.file = None
        self.header = None
        self.header_size = 0
        self.record_size = 0
        self.num_records = 0
        self.record_duration = 0
        self.sampling_rates = None
        self.scalefac = None
        self.dc = None
        self.selected_channels = None
        
    def __enter__(self):
        self.open()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
        
    def open(self):
        """Open the EDF file and read header"""
        self.file = open(self.filename, 'rb')
        self.header = self._read_header()
        self._calculate_scaling_factors()
        
    def close(self):
        """Close the EDF file"""
        if self.file:
            self.file.close()
            self.file = None
            
    def _read_header(self) -> Dict[str, Any]:
        """Read EDF header information"""
        hdr = {}
        
        # Read fixed header
        hdr['ver'] = int(self.file.read(8).decode('ascii').strip())
        hdr['patientID'] = self.file.read(80).decode('ascii').strip()
        hdr['recordID'] = self.file.read(80).decode('ascii').strip()
        hdr['startdate'] = self.file.read(8).decode('ascii').strip()
        hdr['starttime'] = self.file.read(8).decode('ascii').strip()
        hdr['bytes'] = int(self.file.read(8).decode('ascii').strip())
        self.file.read(44)  # Reserved
        hdr['records'] = int(self.file.read(8).decode('ascii').strip())
        hdr['duration'] = float(self.file.read(8).decode('ascii').strip())
        hdr['ns'] = int(self.file.read(4).decode('ascii').strip())
        
        self.num_records = hdr['records']
        self.record_duration = hdr['duration']
        
        # Read signal-specific information
        ns = hdr['ns']
        hdr['label'] = [re.sub(r'\W+$', '', self.file.read(16).decode('ascii').strip()) 
                       for _ in range(ns)]
        hdr['transducer'] = [self.file.read(80).decode('ascii').strip() for _ in range(ns)]
        hdr['units'] = [self.file.read(8).decode('ascii').strip() for _ in range(ns)]
        hdr['physicalMin'] = np.array([float(self.file.read(8).decode('ascii').strip()) 
                                      for _ in range(ns)])
        hdr['physicalMax'] = np.array([float(self.file.read(8).decode('ascii').strip()) 
                                      for _ in range(ns)])
        hdr['digitalMin'] = np.array([int(self.file.read(8).decode('ascii').strip()) 
                                     for _ in range(ns)])
        hdr['digitalMax'] = np.array([int(self.file.read(8).decode('ascii').strip()) 
                                     for _ in range(ns)])
        hdr['prefilter'] = [self.file.read(80).decode('ascii').strip() for _ in range(ns)]
        hdr['samples'] = np.array([int(self.file.read(8).decode('ascii').strip()) 
                                  for _ in range(ns)])
        
        # Skip reserved bytes
        for _ in range(ns):
            self.file.read(32)
            
        hdr['frequency'] = hdr['samples'] / hdr['duration']
        
        # Store header size and calculate record size
        self.header_size = hdr['bytes']
        self.record_size = sum(hdr['samples']) * 2  # 2 bytes per sample
        self.sampling_rates = hdr['frequency']
        
        return hdr
        
    def _calculate_scaling_factors(self):
        """Calculate scaling factors for converting digital to physical values"""
        self.scalefac = ((self.header['physicalMax'] - self.header['physicalMin']) / 
                        (self.header['digitalMax'] - self.header['digitalMin']))
        self.dc = self.header['physicalMax'] - self.scalefac * self.header['digitalMax']
        
    def select_channels(self, channel_indices: Optional[List[int]] = None):
        """
        Select specific channels to read
        
        Args:
            channel_indices: List of channel indices to read. If None, read all channels.
        """
        if channel_indices is None:
            self.selected_channels = list(range(self.header['ns']))
        else:
            self.selected_channels = channel_indices
            
    def read_records(self, start_record: int, num_records: int) -> np.ndarray:
        """
        Read specific records from the file
        
        Args:
            start_record: Starting record index
            num_records: Number of records to read
            
        Returns:
            Data array with shape (channels, samples)
        """
        if self.selected_channels is None:
            self.select_channels()
            
        # Seek to the start of the requested records
        self.file.seek(self.header_size + start_record * self.record_size)
        
        # Read the records
        all_data = []
        for _ in range(num_records):
            record_data = []
            for ch_idx in range(self.header['ns']):
                samples = self.header['samples'][ch_idx]
                if ch_idx in self.selected_channels:
                    # Read and scale the data
                    raw_data = np.frombuffer(self.file.read(samples * 2), dtype=np.int16)
                    scaled_data = raw_data * self.scalefac[ch_idx] + self.dc[ch_idx]
                    record_data.append(scaled_data)
                else:
                    # Skip this channel
                    self.file.seek(samples * 2, 1)
                    
            all_data.append(record_data)
            
        # Concatenate all records
        if all_data:
            data = np.concatenate([np.array(rec) for rec in all_data], axis=1)
        else:
            data = np.array([])
            
        return data
        
    def iter_chunks(self, start_time: float = 0, end_time: Optional[float] = None) -> Iterator[Tuple[np.ndarray, float, float]]:
        """
        Iterate over chunks of the EDF file
        
        Args:
            start_time: Start time in seconds
            end_time: End time in seconds (None for entire file)
            
        Yields:
            Tuple of (data, chunk_start_time, chunk_end_time)
        """
        if end_time is None:
            end_time = self.num_records * self.record_duration
            
        # Calculate records per chunk
        records_per_chunk = int(np.ceil(self.chunk_duration / self.record_duration))
        
        # Calculate starting record
        start_record = int(start_time / self.record_duration)
        end_record = int(np.ceil(end_time / self.record_duration))
        
        current_record = start_record
        while current_record < end_record:
            # Calculate how many records to read
            records_to_read = min(records_per_chunk, end_record - current_record)
            
            # Read the chunk
            chunk_data = self.read_records(current_record, records_to_read)
            
            # Calculate time boundaries
            chunk_start_time = current_record * self.record_duration
            chunk_end_time = min((current_record + records_to_read) * self.record_duration, end_time)
            
            # Trim data if needed (for precise start/end times)
            if chunk_data.size > 0:
                samples_per_sec = self.sampling_rates[0]  # Assuming uniform sampling rate
                
                # Trim start
                if chunk_start_time < start_time:
                    trim_samples = int((start_time - chunk_start_time) * samples_per_sec)
                    chunk_data = chunk_data[:, trim_samples:]
                    chunk_start_time = start_time
                    
                # Trim end
                if chunk_end_time > end_time:
                    trim_samples = int((chunk_end_time - end_time) * samples_per_sec)
                    if trim_samples > 0:
                        chunk_data = chunk_data[:, :-trim_samples]
                    chunk_end_time = end_time
                    
            yield chunk_data, chunk_start_time, chunk_end_time
            
            current_record += records_to_read
            
    def get_header(self) -> Dict[str, Any]:
        """Get the EDF header"""
        return self.header.copy() if self.header else None
        
    def get_channel_data_lazy(self, channel_indices: List[int], 
                             start_time: float = 0, 
                             duration: Optional[float] = None) -> np.ndarray:
        """
        Read specific channels for a specific time range without loading entire file
        
        Args:
            channel_indices: List of channel indices to read
            start_time: Start time in seconds
            duration: Duration in seconds (None for until end)
            
        Returns:
            Data array with shape (channels, samples)
        """
        self.select_channels(channel_indices)
        
        if duration is None:
            end_time = self.num_records * self.record_duration
        else:
            end_time = start_time + duration
            
        all_chunks = []
        for chunk_data, _, _ in self.iter_chunks(start_time, end_time):
            all_chunks.append(chunk_data)
            
        if all_chunks:
            return np.concatenate(all_chunks, axis=1)
        else:
            return np.array([])
