"""
Result formatting module for HFO detection.
Handles formatting and organization of HFO detection results.
"""

import numpy as np
from typing import Dict, List, Any, Optional


class HFOResultFormatter:
    """Formats HFO detection results for output"""
    
    @staticmethod
    def format_results(
        win_len2, std<PERSON><PERSON><PERSON>, mean<PERSON><PERSON><PERSON>, RMS2,
        rejected_start_ind, rejected_end_ind,
        rejecLONG_start_ind, rejecLONG_end_ind,
        final_start_ind, final_end_ind,
        lfo_start_ind, lfo_end_ind,
        noise_start_ind, noise_end_ind,
        myEEG, channel_labels, end_channel,
        con_fact, con_factN,
        datalen_resulted_sec, num_pts, samp_freq, num_min,
        counter, duration, peak_freq, my_amp, hfo_power, max_freq,
        avg_peak_freq, avg_my_amp, avg_hfo_power, avg_max_freq, avg_duration,
        input_file_path
    ) -> Dict[str, Any]:
        """
        Format all HFO detection results into a structured dictionary.
        
        This function was extracted from the main algorithm to improve organization
        while preserving the exact data structure and format.
        """
        result = {
            "win_len2": win_len2,
            "stdHilbert": stdHilbert,
            "meanHilbert": meanHilbert,
            "RMS2": RMS2,
            "rejected_start_ind": rejected_start_ind,
            "rejected_end_ind": rejected_end_ind,
            "rejecLONG_start_ind": rejecLONG_start_ind,
            "rejecLONG_end_ind": rejecLONG_end_ind,
            "final_start_ind": final_start_ind,
            "final_end_ind": final_end_ind,
            "lfo_start_ind": lfo_start_ind,
            "lfo_end_ind": lfo_end_ind,
            "noise_start_ind": noise_start_ind,
            "noise_end_ind": noise_end_ind,
            "myEEG": myEEG,
            "channel_labels": channel_labels,
            "end_channel": end_channel,
            "con_fact": con_fact,
            "con_factN": con_factN,
            "datalen_resulted_sec": datalen_resulted_sec,
            "num_pts": num_pts,
            "samp_freq": samp_freq,
            "num_min": num_min,
            "counter": counter,
            "duration": duration,
            "peak_freq": peak_freq,
            "my_amp": my_amp,
            "hfo_power": hfo_power,
            "max_freq": max_freq,
            "avg_peak_freq": avg_peak_freq,
            "avg_my_amp": avg_my_amp,
            "avg_hfo_power": avg_hfo_power,
            "avg_max_freq": avg_max_freq,
            "avg_duration": avg_duration,
            "input_file_path": input_file_path,
            "success": True
        }
        
        return result
    
    @staticmethod
    def calculate_average_characteristics(
        duration: List[List],
        hfo_power: List[List],
        peak_freq: List[List],
        my_amp: List[List],
        max_freq: List[List],
        num_channels: int,
        start_channel: int,
        end_channel: int
    ) -> tuple:
        """
        Calculate average HFO characteristics per channel.
        
        Returns:
            Tuple of (avg_duration, avg_hfo_power, avg_peak_freq, avg_my_amp, avg_max_freq)
        """
        # Initialize average values
        avg_duration = np.zeros(num_channels)
        avg_hfo_power = np.zeros(num_channels)
        avg_peak_freq = np.zeros(num_channels)
        avg_my_amp = np.zeros(num_channels)
        avg_max_freq = np.zeros(num_channels)
        
        # Calculate averages for each channel
        for j in range(start_channel - 1, end_channel):
            if duration[j]:
                avg_duration[j] = np.mean(duration[j])
            if hfo_power[j]:
                avg_hfo_power[j] = np.mean(np.log10(hfo_power[j]))  # Log10 for power
            if peak_freq[j]:
                avg_peak_freq[j] = np.mean(peak_freq[j])
            if my_amp[j]:
                avg_my_amp[j] = np.mean(my_amp[j])
            if max_freq[j]:
                avg_max_freq[j] = np.mean(max_freq[j])
        
        return avg_duration, avg_hfo_power, avg_peak_freq, avg_my_amp, avg_max_freq
    
    @staticmethod
    def convert_indices_to_arrays(
        final_start_ind: List[List],
        final_end_ind: List[List],
        lfo_start_ind: List[List],
        lfo_end_ind: List[List],
        noise_start_ind: List[List],
        noise_end_ind: List[List]
    ) -> tuple:
        """
        Convert list indices to numpy arrays for consistency.
        
        Returns:
            Tuple of converted arrays
        """
        final_start_ind = [np.array(lst) for lst in final_start_ind]
        final_end_ind = [np.array(lst) for lst in final_end_ind]
        lfo_start_ind = [np.array(lst) for lst in lfo_start_ind]
        lfo_end_ind = [np.array(lst) for lst in lfo_end_ind]
        noise_start_ind = [np.array(lst) for lst in noise_start_ind]
        noise_end_ind = [np.array(lst) for lst in noise_end_ind]
        
        return (final_start_ind, final_end_ind, lfo_start_ind, 
                lfo_end_ind, noise_start_ind, noise_end_ind)