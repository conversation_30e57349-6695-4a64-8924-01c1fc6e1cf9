import numpy as np
from typing import Dict, Any, List, Tuple, Optional
from .base_validator import BaseValidator

class SignalValidator(BaseValidator):
    """Validator for EEG signal quality and processing requirements"""
    
    # Signal quality thresholds
    MAX_FLAT_DURATION_MS = 100  # Maximum duration of flat signal in milliseconds
    MIN_SIGNAL_VARIANCE = 1e-10  # Minimum variance to consider signal as non-flat
    MAX_DISCONTINUITY_MS = 50  # Maximum discontinuity length in milliseconds
    
    def __init__(self):
        super().__init__()
        
    def validate_signal_quality(self, signal_data: np.ndarray, 
                               sampling_rate: float,
                               channel_labels: Optional[List[str]] = None) -> Tuple[bool, List[str]]:
        """
        Validate EEG signal quality for processing
        
        Args:
            signal_data: 2D numpy array (channels x samples)
            sampling_rate: Sampling rate in Hz
            channel_labels: Optional list of channel labels
            
        Returns:
            Tuple[bool, List[str]]: (is_valid, error_messages)
        """
        self.clear_messages()
        
        if not isinstance(signal_data, np.ndarray):
            self.add_error(f"Signal data must be numpy array, got {type(signal_data).__name__}")
            return False, self.errors
        
        if signal_data.ndim != 2:
            self.add_error(f"Signal data must be 2D array (channels x samples), got {signal_data.ndim}D")
            return False, self.errors
        
        n_channels, n_samples = signal_data.shape
        
        # Check for empty data
        if n_channels == 0:
            self.add_error("No channels in signal data")
        if n_samples == 0:
            self.add_error("No samples in signal data")
        
        if self.has_errors():
            return False, self.errors
        
        # Check each channel for quality issues
        for ch_idx in range(n_channels):
            channel_data = signal_data[ch_idx, :]
            ch_label = channel_labels[ch_idx] if channel_labels and ch_idx < len(channel_labels) else f"Channel {ch_idx}"
            
            # Check for flat signals
            self._check_flat_signal(channel_data, sampling_rate, ch_label)
            
            # Check for NaN or Inf values
            self._check_invalid_values(channel_data, ch_label)
            
            # Check for signal discontinuities
            self._check_discontinuities(channel_data, sampling_rate, ch_label)
            
            # Check signal variance
            self._check_signal_variance(channel_data, ch_label)
        
        return not self.has_errors(), self.errors
    
    def validate_blanks(self, signal_data: np.ndarray,
                       channel_labels: List[str]) -> Tuple[List[int], List[int]]:
        """
        Find and validate blank sections in the signal (ported from find_blanks.py)
        
        Args:
            signal_data: 2D numpy array (channels x samples)
            channel_labels: List of channel labels
            
        Returns:
            Tuple[blank_starts, blank_ends]: Start and end indices of blank sections
        """
        blank_starts = []
        blank_ends = []
        
        n_channels, n_samples = signal_data.shape
        
        # Find flat/blank sections for each channel
        for ch_idx in range(n_channels):
            channel_data = signal_data[ch_idx, :]
            
            # Calculate difference between consecutive samples
            diff = np.diff(channel_data)
            
            # Find where signal is flat (difference is zero or very small)
            flat_mask = np.abs(diff) < 1e-10
            
            # Find start and end of flat sections
            flat_changes = np.diff(np.concatenate(([0], flat_mask.astype(int), [0])))
            flat_starts = np.where(flat_changes == 1)[0]
            flat_ends = np.where(flat_changes == -1)[0]
            
            # Filter out very short flat sections (< 10 samples)
            min_flat_length = 10
            for start, end in zip(flat_starts, flat_ends):
                if end - start >= min_flat_length:
                    blank_starts.append(start)
                    blank_ends.append(end)
        
        # Convert to numpy arrays and sort
        blank_starts = np.array(blank_starts)
        blank_ends = np.array(blank_ends)
        
        if len(blank_starts) > 0:
            # Sort by start time
            sort_idx = np.argsort(blank_starts)
            blank_starts = blank_starts[sort_idx]
            blank_ends = blank_ends[sort_idx]
            
            # Merge overlapping blanks
            merged_starts = []
            merged_ends = []
            
            current_start = blank_starts[0]
            current_end = blank_ends[0]
            
            for start, end in zip(blank_starts[1:], blank_ends[1:]):
                if start <= current_end:
                    # Overlapping or adjacent - merge
                    current_end = max(current_end, end)
                else:
                    # Non-overlapping - save current and start new
                    merged_starts.append(current_start)
                    merged_ends.append(current_end)
                    current_start = start
                    current_end = end
            
            # Add the last blank
            merged_starts.append(current_start)
            merged_ends.append(current_end)
            
            blank_starts = np.array(merged_starts)
            blank_ends = np.array(merged_ends)
        
        return blank_starts, blank_ends
    
    def validate_montage_compatibility(self, signal_data: np.ndarray,
                                      montage_type: str,
                                      reference_channels: Optional[List[int]] = None) -> Tuple[bool, List[str]]:
        """
        Validate signal compatibility with selected montage
        
        Args:
            signal_data: 2D numpy array (channels x samples)
            montage_type: Type of montage ('bipolar', 'average', 'referential')
            reference_channels: Indices of reference channels for referential montage
            
        Returns:
            Tuple[bool, List[str]]: (is_valid, error_messages)
        """
        self.clear_messages()
        
        n_channels, _ = signal_data.shape
        
        if montage_type == 'bipolar':
            if n_channels < 2:
                self.add_error(f"Bipolar montage requires at least 2 channels, found {n_channels}")
        
        elif montage_type == 'referential':
            if not reference_channels:
                self.add_error("Referential montage requires reference channels to be specified")
            else:
                invalid_refs = [idx for idx in reference_channels if idx >= n_channels or idx < 0]
                if invalid_refs:
                    self.add_error(f"Invalid reference channel indices: {invalid_refs} (valid range: 0-{n_channels-1})")
        
        elif montage_type == 'average':
            if n_channels < 2:
                self.add_warning(f"Average montage typically requires multiple channels, found {n_channels}")
        
        return not self.has_errors(), self.errors
    
    def _check_flat_signal(self, channel_data: np.ndarray, 
                          sampling_rate: float, 
                          channel_label: str):
        """Check for flat signal sections"""
        # Find consecutive identical values
        diff = np.diff(channel_data)
        flat_mask = diff == 0
        
        if np.any(flat_mask):
            # Find runs of flat signal
            flat_changes = np.diff(np.concatenate(([0], flat_mask.astype(int), [0])))
            flat_starts = np.where(flat_changes == 1)[0]
            flat_ends = np.where(flat_changes == -1)[0]
            
            # Check duration of flat sections
            max_flat_samples = int(self.MAX_FLAT_DURATION_MS * sampling_rate / 1000)
            
            for start, end in zip(flat_starts, flat_ends):
                flat_duration_samples = end - start
                if flat_duration_samples > max_flat_samples:
                    flat_duration_ms = flat_duration_samples * 1000 / sampling_rate
                    self.add_warning(
                        f"{channel_label}: Flat signal detected for {flat_duration_ms:.1f}ms "
                        f"at sample {start}"
                    )
    
    def _check_invalid_values(self, channel_data: np.ndarray, channel_label: str):
        """Check for NaN or Inf values in signal"""
        nan_count = np.sum(np.isnan(channel_data))
        inf_count = np.sum(np.isinf(channel_data))
        
        if nan_count > 0:
            self.add_error(f"{channel_label}: Found {nan_count} NaN values")
        
        if inf_count > 0:
            self.add_error(f"{channel_label}: Found {inf_count} Inf values")
    
    def _check_discontinuities(self, channel_data: np.ndarray, 
                              sampling_rate: float,
                              channel_label: str):
        """Check for signal discontinuities (sudden jumps)"""
        # Calculate derivative
        diff = np.diff(channel_data)
        
        # Calculate robust estimate of normal variation (MAD - Median Absolute Deviation)
        mad = np.median(np.abs(diff - np.median(diff)))
        
        if mad > 0:
            # Threshold for discontinuity (10 times the MAD)
            threshold = 10 * mad
            
            # Find potential discontinuities
            discontinuities = np.where(np.abs(diff) > threshold)[0]
            
            if len(discontinuities) > 0:
                # Check if discontinuities are isolated (not part of normal signal variation)
                for disc_idx in discontinuities[:5]:  # Check first 5 discontinuities
                    # Look at surrounding samples
                    window_size = int(0.01 * sampling_rate)  # 10ms window
                    start_idx = max(0, disc_idx - window_size)
                    end_idx = min(len(diff), disc_idx + window_size)
                    
                    window_diff = diff[start_idx:end_idx]
                    if len(window_diff) > 0:
                        # If this is an isolated jump (not part of high-frequency activity)
                        if np.sum(np.abs(window_diff) > threshold) == 1:
                            self.add_warning(
                                f"{channel_label}: Potential discontinuity at sample {disc_idx} "
                                f"(jump of {diff[disc_idx]:.2f})"
                            )
    
    def _check_signal_variance(self, channel_data: np.ndarray, channel_label: str):
        """Check if signal has sufficient variance"""
        variance = np.var(channel_data)
        
        if variance < self.MIN_SIGNAL_VARIANCE:
            self.add_error(
                f"{channel_label}: Signal variance too low ({variance:.2e}), "
                "possible flat or disconnected channel"
            )
        
        # Check for unusually high variance (possible noise)
        if variance > 1e6:
            self.add_warning(
                f"{channel_label}: Very high signal variance ({variance:.2e}), "
                "possible noise or scaling issue"
            )
    
    def validate_processing_requirements(self, signal_data: np.ndarray,
                                        sampling_rate: float,
                                        low_cutoff: float,
                                        high_cutoff: float) -> Tuple[bool, List[str]]:
        """
        Validate signal meets requirements for HFO processing
        
        Args:
            signal_data: 2D numpy array (channels x samples)  
            sampling_rate: Sampling rate in Hz
            low_cutoff: Low frequency cutoff for filtering
            high_cutoff: High frequency cutoff for filtering
            
        Returns:
            Tuple[bool, List[str]]: (is_valid, error_messages)
        """
        self.clear_messages()
        
        n_channels, n_samples = signal_data.shape
        
        # Check minimum signal length for filtering
        min_duration_sec = 1.0  # Minimum 1 second for reliable filtering
        min_samples = int(min_duration_sec * sampling_rate)
        
        if n_samples < min_samples:
            self.add_error(
                f"Signal too short for processing: {n_samples} samples "
                f"({n_samples/sampling_rate:.2f}s), minimum required: {min_samples} samples ({min_duration_sec}s)"
            )
        
        # Check filter requirements
        filter_order = 4  # Typical butterworth filter order
        transition_band = 10  # Hz
        
        # Check if there's enough frequency resolution
        freq_resolution = sampling_rate / n_samples
        if freq_resolution > transition_band:
            self.add_warning(
                f"Limited frequency resolution ({freq_resolution:.2f}Hz) for filtering. "
                f"Consider longer signal segments for better filtering."
            )
        
        # Check for edge effects based on filter cutoffs
        # High-pass filter settling time
        highpass_settling_samples = int(10 / low_cutoff * sampling_rate) if low_cutoff > 0 else 0
        
        if highpass_settling_samples > n_samples / 10:
            self.add_warning(
                f"High-pass filter settling time ({highpass_settling_samples} samples) "
                f"is significant compared to signal length ({n_samples} samples). "
                "Edge effects may impact results."
            )
        
        return not self.has_errors(), self.errors
    
    def validate(self, data: Any) -> Tuple[bool, List[str]]:
        """
        Main validation method
        
        Args:
            data: Dictionary containing signal_data, sampling_rate, and other parameters
            
        Returns:
            Tuple[bool, List[str]]: (is_valid, error_messages)
        """
        self.clear_messages()
        
        if not isinstance(data, dict):
            self.add_error(f"Invalid data type for validation: expected dict, got {type(data).__name__}")
            return False, self.errors
        
        signal_data = data.get('signal_data')
        sampling_rate = data.get('sampling_rate')
        
        if signal_data is None:
            self.add_error("Missing required field: signal_data")
        if sampling_rate is None:
            self.add_error("Missing required field: sampling_rate")
        
        if self.has_errors():
            return False, self.errors
        
        # Validate signal quality
        channel_labels = data.get('channel_labels')
        valid, _ = self.validate_signal_quality(signal_data, sampling_rate, channel_labels)
        
        # Validate processing requirements if frequency parameters provided
        if 'low_cutoff' in data and 'high_cutoff' in data:
            proc_valid, _ = self.validate_processing_requirements(
                signal_data, sampling_rate,
                data['low_cutoff'], data['high_cutoff']
            )
            valid = valid and proc_valid
        
        # Validate montage compatibility if provided
        if 'montage_type' in data:
            mont_valid, _ = self.validate_montage_compatibility(
                signal_data, data['montage_type'],
                data.get('reference_channels')
            )
            valid = valid and mont_valid
        
        return valid, self.errors