from typing import List, Optional, Dict, Any

class ValidationError(Exception):
    """Base exception for validation errors"""
    
    def __init__(self, message: str, errors: Optional[List[str]] = None, 
                 field: Optional[str] = None):
        self.message = message
        self.errors = errors or []
        self.field = field
        super().__init__(self.message)
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for API responses"""
        return {
            "error": self.__class__.__name__,
            "message": self.message,
            "field": self.field,
            "details": self.errors
        }

class EDFValidationError(ValidationError):
    """Exception raised for EDF file validation errors"""
    
    def __init__(self, message: str, errors: Optional[List[str]] = None,
                 filename: Optional[str] = None):
        super().__init__(message, errors)
        self.filename = filename

class ParameterValidationError(ValidationError):
    """Exception raised for parameter validation errors"""
    
    def __init__(self, message: str, parameter_name: Optional[str] = None,
                 invalid_value: Any = None, valid_range: Optional[str] = None):
        super().__init__(message, field=parameter_name)
        self.parameter_name = parameter_name
        self.invalid_value = invalid_value
        self.valid_range = valid_range
        
    def to_dict(self) -> Dict[str, Any]:
        result = super().to_dict()
        result.update({
            "parameter": self.parameter_name,
            "invalid_value": self.invalid_value,
            "valid_range": self.valid_range
        })
        return result

class SignalValidationError(ValidationError):
    """Exception raised for signal processing validation errors"""
    
    def __init__(self, message: str, channel: Optional[str] = None,
                 time_range: Optional[List[float]] = None):
        super().__init__(message)
        self.channel = channel
        self.time_range = time_range

class MontageValidationError(ValidationError):
    """Exception raised for montage configuration errors"""
    
    def __init__(self, message: str, montage_type: Optional[str] = None,
                 required_channels: Optional[int] = None):
        super().__init__(message)
        self.montage_type = montage_type
        self.required_channels = required_channels

class FrequencyValidationError(ValidationError):
    """Exception raised for frequency filter validation errors"""
    
    def __init__(self, message: str, low_cutoff: Optional[float] = None,
                 high_cutoff: Optional[float] = None, 
                 sampling_rate: Optional[float] = None):
        super().__init__(message)
        self.low_cutoff = low_cutoff
        self.high_cutoff = high_cutoff
        self.sampling_rate = sampling_rate
        
    def to_dict(self) -> Dict[str, Any]:
        result = super().to_dict()
        result.update({
            "low_cutoff": self.low_cutoff,
            "high_cutoff": self.high_cutoff,
            "sampling_rate": self.sampling_rate,
            "max_allowed_frequency": self.sampling_rate / 3 if self.sampling_rate else None
        })
        return result

class TimeSegmentValidationError(ValidationError):
    """Exception raised for time segment validation errors"""
    
    def __init__(self, message: str, start_time: Optional[float] = None,
                 end_time: Optional[float] = None, 
                 file_duration: Optional[float] = None):
        super().__init__(message)
        self.start_time = start_time
        self.end_time = end_time
        self.file_duration = file_duration