from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from typing import Any
import logging
import traceback

from .validation_exceptions import ValidationError

logger = logging.getLogger(__name__)

async def validation_error_handler(request: Request, exc: ValidationError) -> JSONResponse:
    """Handle validation errors and return appropriate HTTP responses"""
    logger.error(f"Validation error: {exc.message}", exc_info=True)
    
    return JSONResponse(
        status_code=400,
        content=exc.to_dict()
    )

async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Handle general exceptions and return appropriate HTTP responses"""
    logger.error(f"Unhandled exception: {str(exc)}", exc_info=True)
    
    return JSONResponse(
        status_code=500,
        content={
            "error": "InternalServerError",
            "message": "An unexpected error occurred",
            "detail": str(exc) if request.app.debug else None
        }
    )

def setup_exception_handlers(app: Any):
    """Setup exception handlers for the FastAPI application"""
    app.add_exception_handler(ValidationError, validation_error_handler)
    app.add_exception_handler(Exception, general_exception_handler)
    
    logger.info("Exception handlers configured")