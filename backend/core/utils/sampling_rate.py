"""
Utility functions for sampling rate extraction and handling.
Consolidates duplicate sampling rate logic from multiple files.
"""

import numpy as np
from typing import Union, List, Optional


class SamplingRateUtils:
    """Utilities for extracting and handling sampling rates from EEG data"""
    
    @staticmethod
    def extract_sampling_rate(frequency_data: Union[np.ndarray, List[float], float]) -> float:
        """
        Extract sampling rate from various data formats.
        
        This consolidates the duplicate logic found in:
        - app.py (lines 105-110, 160-165, 337-342)
        - analysis_service.py (lines 442-478)
        
        Args:
            frequency_data: Can be:
                - numpy array of frequencies
                - list of frequencies
                - single frequency value
                
        Returns:
            float: The sampling rate (uses first value if array/list)
            
        Raises:
            ValueError: If frequency_data is invalid or empty
        """
        if frequency_data is None:
            raise ValueError("Frequency data cannot be None")
            
        # Handle numpy arrays
        if isinstance(frequency_data, np.ndarray):
            if frequency_data.size == 0:
                raise ValueError("Empty frequency array")
            # Take first value if array
            return float(frequency_data.flat[0])
            
        # Handle lists
        if isinstance(frequency_data, (list, tuple)):
            if len(frequency_data) == 0:
                raise ValueError("Empty frequency list")
            # Take first value if list
            return float(frequency_data[0])
            
        # Handle scalar values
        if isinstance(frequency_data, (int, float)):
            return float(frequency_data)
            
        # Try to convert to float as last resort
        try:
            return float(frequency_data)
        except (TypeError, ValueError) as e:
            raise ValueError(f"Cannot extract sampling rate from {type(frequency_data)}: {e}")
    
    @staticmethod
    def validate_sampling_rate(srate: float, min_rate: float = 200) -> bool:
        """
        Validate if sampling rate is sufficient for HFO detection.
        
        Args:
            srate: The sampling rate to validate
            min_rate: Minimum required rate (default 200 Hz for HFO)
            
        Returns:
            bool: True if valid, False otherwise
        """
        return srate >= min_rate
    
    @staticmethod
    def calculate_nyquist_frequency(srate: float) -> float:
        """
        Calculate Nyquist frequency (maximum frequency that can be represented).
        
        Args:
            srate: Sampling rate
            
        Returns:
            float: Nyquist frequency (srate / 2)
        """
        return srate / 2.0
    
    @staticmethod
    def calculate_max_analysis_frequency(srate: float) -> float:
        """
        Calculate maximum frequency for HFO analysis.
        Per the algorithm: MAX_FREQ = round(samp_freq / 3)
        
        Args:
            srate: Sampling rate
            
        Returns:
            float: Maximum analysis frequency
        """
        return round(srate / 3.0)
    
    @staticmethod
    def get_uniform_sampling_rate(frequencies: Union[np.ndarray, List[float]]) -> Optional[float]:
        """
        Check if all channels have the same sampling rate and return it.
        
        Args:
            frequencies: Array or list of sampling frequencies
            
        Returns:
            float: The uniform sampling rate if all are the same
            None: If frequencies vary across channels
        """
        if isinstance(frequencies, np.ndarray):
            freq_list = frequencies.flatten().tolist()
        else:
            freq_list = list(frequencies)
            
        if not freq_list:
            return None
            
        # Check if all frequencies are the same
        first_freq = freq_list[0]
        if all(f == first_freq for f in freq_list):
            return float(first_freq)
            
        return None