import re
import numpy as np
import logging
import asyncio
from typing import Dict, List, Any, Optional

from core.utils import SamplingRateUtils
from models.parameters import AnalysisParameters
from core.validators import EDFValidator, ParameterValidator, SignalValidator
from core.exceptions.validation_exceptions import ValidationError

from .chunked_hfo_detector import ChunkedHFODetector
from .chunked_data_preprocessor import ChunkedDataPreprocessor
from .result_formatter import ResultFormatter

logger = logging.getLogger(__name__)


class ChunkedEEGStreamProcessor:
    """Processes EEG files in chunks with real-time HFO detection"""
    
    def __init__(self, file_path: str, parameters: AnalysisParameters, chunk_duration: float = 10.0):
        self.file_path = file_path
        self.parameters = parameters
        self.chunk_duration = chunk_duration
        
        # Initialize validators
        self.edf_validator = EDFValidator()
        self.param_validator = ParameterValidator()
        self.signal_validator = SignalValidator()
        
        # Initialize chunked components
        self.hfo_detector = ChunkedHFODetector()
        self.data_preprocessor = ChunkedDataPreprocessor(chunk_duration)
        self.result_formatter = ResultFormatter()
        
        # State tracking
        self.is_initialized = False
        self.total_chunks = 0
        self.processed_chunks = 0
        self.header = None
        self.sampling_rate = None
        
    async def initialize(self):
        """Initialize processor with validation but without loading full file"""
        try:
            # Validate file first
            is_valid, errors = self.edf_validator.validate_file(self.file_path)
            if not is_valid:
                raise ValidationError(
                    message="EDF file validation failed during initialization",
                    errors=errors
                )
            
            # Initialize preprocessor (loads only header)
            self.header = self.data_preprocessor.initialize(self.file_path)
            
            # Validate header
            is_valid, errors = self.edf_validator.validate_header(self.header)
            if not is_valid:
                raise ValidationError(
                    message="EDF header validation failed",
                    errors=errors
                )
            
            # Apply channel selection
            if hasattr(self.parameters, 'channel_selection') and self.parameters.channel_selection:
                try:
                    self.data_preprocessor.subset_channels(self.parameters.channel_selection)
                except Exception as e:
                    logger.warning(f"Failed to subset channels: {e}")
            
            # Get sampling rate
            self.sampling_rate = self._get_sampling_rate()
            
            # Validate first chunk for signal quality
            await self._validate_first_chunk()
            
            # Initialize HFO detector with parameters
            montage_type = self._get_montage_type()
            user_ref = ""
            if self.parameters.montage.type == "referential":
                user_ref = self.parameters.montage.reference_channel or ""
                
            success = self.hfo_detector.initialize_detection(
                self.header,
                self.parameters,
                montage_type,
                user_ref
            )
            
            if not success:
                raise RuntimeError("Failed to initialize HFO detector")
            
            # Calculate total chunks
            self.total_chunks = self.data_preprocessor.get_total_chunks()
            
            self.is_initialized = True
            logger.info(f"Initialized chunked processor: {self.total_chunks} chunks to process")
            
            return True
            
        except ValidationError:
            raise
        except Exception as e:
            logger.error(f"Error initializing chunked processor: {e}")
            raise
            
    async def _validate_first_chunk(self):
        """Validate signal quality of first chunk"""
        try:
            # Get first chunk
            first_chunk_data, _, _ = self.data_preprocessor.get_chunk(0)
            
            if first_chunk_data.size == 0:
                raise ValidationError(message="No data available in first chunk")
                
            # Get labels for validation
            if self.data_preprocessor.selected_channel_indices:
                labels = [self.header['label'][i] for i in self.data_preprocessor.selected_channel_indices]
            else:
                labels = self.header['label']
                
            # Validate signal quality
            is_valid, errors = self.signal_validator.validate_signal_quality(
                first_chunk_data, self.sampling_rate, labels
            )
            
            if not is_valid:
                logger.warning(f"Signal quality issues detected: {errors}")
                
        except Exception as e:
            logger.error(f"Error validating first chunk: {e}")
            # Continue anyway - don't fail initialization due to validation
            
    def _get_montage_type(self) -> str:
        """Get properly formatted montage type"""
        montage = self.parameters.montage.type
        if montage == "bipolar":
            return "Bipolar"
        elif montage == "average":
            return "Average"
        elif montage == "referential":
            return "Referential"
        return montage
        
    def _get_sampling_rate(self) -> float:
        """Extract sampling rate from header"""
        if self.header is None:
            raise ValueError("No header information available")
            
        freq = self.header.get('frequency')
        if freq is None:
            raise ValueError("No frequency information found in EDF header")
        return SamplingRateUtils.extract_sampling_rate(freq)
        
    async def process_preview(self) -> Dict[str, Any]:
        """Process first 30 seconds for immediate preview"""
        try:
            if not self.is_initialized:
                await self.initialize()
                
            preview_duration = 30  # seconds
            preview_chunks = int(np.ceil(preview_duration / self.chunk_duration))
            
            # Process preview chunks
            preview_hfo_count = 0
            for i in range(min(preview_chunks, self.total_chunks)):
                chunk_data, start_time, end_time = self.data_preprocessor.get_chunk(i)
                
                # Quick HFO detection for preview
                chunk_result = await self.hfo_detector.process_chunk(
                    chunk_data, start_time, end_time, i
                )
                preview_hfo_count += chunk_result.get('hfo_count', 0)
                
            # Get channel labels
            if self.data_preprocessor.selected_channel_indices:
                labels = [self.header['label'][i] for i in self.data_preprocessor.selected_channel_indices]
            else:
                labels = self.header['label']
                
            return {
                "preview_duration": preview_duration,
                "channels": labels,
                "sampling_rate": self.sampling_rate,
                "quick_hfo_count": preview_hfo_count,
                "message": "Preview ready, full analysis continuing..."
            }
            
        except Exception as e:
            logger.error(f"Error processing preview: {e}")
            return {"error": str(e)}
            
    def get_total_chunks(self) -> int:
        """Get total number of chunks to process"""
        return self.total_chunks
        
    async def process_chunk(self, chunk_num: int, 
                          send_hfo_callback: Optional[Any] = None) -> Dict[str, Any]:
        """
        Process a specific chunk of the EEG data with HFO detection
        
        Args:
            chunk_num: Chunk number to process
            send_hfo_callback: Optional callback to send HFO results immediately
        """
        try:
            if not self.is_initialized:
                await self.initialize()
                
            # Get chunk data
            chunk_data, start_time, end_time = self.data_preprocessor.get_chunk(chunk_num)
            
            if chunk_data.size == 0:
                logger.warning(f"Empty chunk {chunk_num}")
                return {
                    "time_range": [start_time, end_time],
                    "hfo_events": [],
                    "channel_data": {},
                    "chunk_number": chunk_num
                }
                
            # Run HFO detection on this chunk
            hfo_result = await self.hfo_detector.process_chunk(
                chunk_data, start_time, end_time, chunk_num
            )
            
            # Extract HFOs for this chunk
            chunk_hfos = []
            if 'hfos' in hfo_result:
                for hfo in hfo_result['hfos']:
                    chunk_hfos.append({
                        'type': 'HFO',
                        'channel': hfo['channel'],
                        'start_time': hfo['start_time'],
                        'end_time': hfo['end_time'],
                        'duration_ms': hfo['duration_ms'],
                        'amplitude': hfo['amplitude'],
                        'frequency': hfo['frequency'],
                        'sample_start': hfo['start_sample'],
                        'sample_end': hfo['end_sample']
                    })
                    
            # Send HFO results immediately if callback provided
            if send_hfo_callback and chunk_hfos:
                await send_hfo_callback({
                    "type": "hfo_batch",
                    "chunk_number": chunk_num,
                    "hfos": chunk_hfos
                })
                
            # Prepare channel data for visualization
            channel_data = self.data_preprocessor.prepare_channel_data(chunk_data)
            
            self.processed_chunks += 1
            
            return {
                "time_range": [start_time, end_time],
                "hfo_events": chunk_hfos,
                "channel_data": channel_data,
                "chunk_number": chunk_num,
                "hfo_count": len(chunk_hfos)
            }
            
        except Exception as e:
            logger.error(f"Error processing chunk {chunk_num}: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return {
                "time_range": [0, 0],
                "hfo_events": [],
                "channel_data": {},
                "chunk_number": chunk_num,
                "error": str(e)
            }
            
    def get_total_hfos(self) -> int:
        """Get total number of HFOs detected so far"""
        return self.hfo_detector.get_total_hfos()
        
    def get_summary(self) -> Dict[str, Any]:
        """Get analysis summary"""
        if not self.header:
            return {}
            
        # Determine number of channels analyzed
        if self.data_preprocessor.selected_channel_indices:
            num_channels = len(self.data_preprocessor.selected_channel_indices)
        else:
            num_channels = len(self.header['label'])
            
        # Format summary
        frequency_range = f"{self.parameters.frequency.low_cutoff}-{self.parameters.frequency.high_cutoff} Hz"
        
        # Get cumulative results
        results = self.hfo_detector.get_cumulative_results()
        
        return self.result_formatter.format_summary(
            self.get_total_hfos(),
            num_channels,
            frequency_range,
            self.parameters.montage.type,
            self.processed_chunks
        )
        
    def cleanup(self):
        """Clean up resources"""
        if self.data_preprocessor:
            self.data_preprocessor.close()


class ChunkedAnalysisService:
    """Service for managing chunked EEG analysis"""
    
    async def create_processor(self, file_path: str, 
                             parameters: AnalysisParameters,
                             chunk_duration: float = 10.0) -> ChunkedEEGStreamProcessor:
        """Create and initialize a chunked EEG processor"""
        processor = ChunkedEEGStreamProcessor(file_path, parameters, chunk_duration)
        await processor.initialize()
        return processor
