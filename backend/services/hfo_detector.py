import sys
import numpy as np
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

# Add hfo_engine to path
sys.path.append('./core/hfo_engine')

from hfo_analysis import run_hfo_algorithm

class HFODetector:
    """Service for HFO detection algorithm execution"""
    
    def __init__(self):
        self.hfo_results = None
        self.total_hfos = 0
        
    def run_detection(self, 
                     EEG: Dict[str, Any],
                     file_path: str,
                     parameters: Any,
                     montage_type: str,
                     user_ref: str = "") -> Optional[Dict[str, Any]]:
        """Run the HFO detection algorithm on the EEG data"""
        try:
            # Map parameters to algorithm inputs
            threshold_option1 = parameters.thresholds.amplitude1
            threshold_option2 = parameters.thresholds.amplitude2
            threshold_option3 = parameters.thresholds.peaks1
            threshold_option4 = parameters.thresholds.peaks2
            threshold_option5 = parameters.thresholds.duration
            threshold_option6 = parameters.thresholds.temporal_sync
            threshold_option7 = parameters.thresholds.spatial_sync
            
            # Determine time segment
            analysis_start = 0
            analysis_end = -1  # -1 means entire file
            
            if parameters.time_segment.mode == "start_end_times":
                # TODO: Parse dates and convert to seconds
                pass
            elif parameters.time_segment.mode == "start_time_duration":
                # TODO: Parse start time and duration
                if hasattr(parameters.time_segment, 'duration_seconds'):
                    analysis_end = parameters.time_segment.duration_seconds
            
            # Get frequency settings
            locutoff = parameters.frequency.low_cutoff
            hicutoff = parameters.frequency.high_cutoff
            
            # Run the HFO detection algorithm
            logger.info(f"Running HFO detection with parameters: {locutoff}-{hicutoff}Hz, montage: {montage_type}")
            self.hfo_results = run_hfo_algorithm(
                EEG,
                file_path,
                analysis_start,
                analysis_end,
                montage_type,
                user_ref,
                locutoff,
                hicutoff,
                None,  # gui_output callback
                threshold_option1,
                threshold_option2,
                threshold_option3,
                threshold_option4,
                threshold_option5,
                threshold_option6,
                threshold_option7
            )
            
            if self.hfo_results and self.hfo_results.get('success'):
                self._calculate_total_hfos()
                logger.info(f"HFO detection completed successfully with {self.total_hfos} HFOs")
                return self.hfo_results
            else:
                logger.warning("HFO detection did not complete successfully")
                return None
                
        except Exception as e:
            logger.error(f"Error running HFO detection: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return None
    
    def _calculate_total_hfos(self):
        """Calculate total HFO count from results"""
        accepted_count = 0
        rejected_count = 0
        rejected_long_count = 0
        
        if 'final_start_ind' in self.hfo_results:
            final_start = self.hfo_results['final_start_ind']
            if hasattr(final_start[0], '__len__'):
                # Count non-zero entries across all channels
                for chan_starts in final_start:
                    accepted_count += np.sum(chan_starts > 0)
            self.total_hfos = accepted_count
        
        if 'rejected_start_ind' in self.hfo_results:
            rejected_start = self.hfo_results['rejected_start_ind']
            if hasattr(rejected_start, 'shape'):
                rejected_count = np.sum(rejected_start > 0)
        
        if 'rejecLONG_start_ind' in self.hfo_results:
            rejected_long_start = self.hfo_results['rejecLONG_start_ind']
            if hasattr(rejected_long_start, 'shape'):
                rejected_long_count = np.sum(rejected_long_start > 0)
        
        logger.info(f"HFO detection results:")
        logger.info(f"  - Accepted HFOs: {accepted_count}")
        logger.info(f"  - Rejected HFOs: {rejected_count}")
        logger.info(f"  - Rejected Long HFOs: {rejected_long_count}")
        logger.info(f"  - Total HFOs: {accepted_count + rejected_count + rejected_long_count}")
    
    def get_results(self) -> Optional[Dict[str, Any]]:
        """Get HFO detection results"""
        return self.hfo_results
    
    def get_total_hfos(self) -> int:
        """Get total number of HFOs detected"""
        return self.total_hfos