"""
EEG data processing service.
Handles EEG data preparation and preprocessing for HFO detection.
"""

import numpy as np
from typing import Dict, Any, List, Optional, Tuple
import logging
from core.utils import SamplingRateUtils, ChannelLabelUtils

logger = logging.getLogger(__name__)


class EEGProcessor:
    """Processes EEG data for analysis"""
    
    def __init__(self):
        """Initialize the EEG processor"""
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def prepare_eeg_data(self, header: Dict[str, Any], data: np.ndarray, 
                        parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare EEG data for HFO detection.
        
        Args:
            header: EDF header information
            data: Raw EEG data
            parameters: Analysis parameters
            
        Returns:
            Dict containing prepared EEG data structure
        """
        # Extract sampling rate
        srate = SamplingRateUtils.extract_sampling_rate(header.get('frequency'))
        
        # Process channel labels
        channel_labels = self._process_channel_labels(header['label'], parameters)
        
        # Create EEG structure
        eeg_struct = {
            'data': data,
            'nbchan': len(channel_labels),
            'srate': srate,
            'pnts': data.shape[1] if len(data.shape) > 1 else len(data),
            'times': np.arange(data.shape[1]) / srate if len(data.shape) > 1 else np.arange(len(data)) / srate,
            'chanlocs': channel_labels,
            'setname': header.get('patientID', 'Unknown'),
            'comments': f"EDF file: {header.get('local_subject_id', 'Unknown')}",
            'xmin': 0,
            'xmax': data.shape[1] / srate if len(data.shape) > 1 else len(data) / srate
        }
        
        return eeg_struct
    
    def _process_channel_labels(self, labels: List[str], parameters: Dict[str, Any]) -> List[str]:
        """
        Process channel labels based on parameters.
        
        Args:
            labels: Original channel labels
            parameters: Analysis parameters including channel selection
            
        Returns:
            List of processed channel labels
        """
        # Check if specific channels are selected
        if parameters.get('channelSelection') and parameters['channelSelection'].get('selectedLeads'):
            selected_leads = parameters['channelSelection']['selectedLeads']
            filtered_labels = []
            
            for lead_config in selected_leads:
                lead_name = lead_config.get('lead', '')
                contacts = lead_config.get('contacts', '')
                
                # Get indices for this lead
                channel_groups = ChannelLabelUtils.extract_channel_groups(labels)
                if lead_name in channel_groups:
                    lead_indices = channel_groups[lead_name]
                    
                    if contacts:
                        # Parse contact specification
                        selected_contacts = ChannelLabelUtils.parse_contact_specification(contacts)
                        for idx in lead_indices:
                            if idx + 1 in selected_contacts:  # Convert to 1-based
                                filtered_labels.append(labels[idx])
                    else:
                        # Include all channels for this lead
                        for idx in lead_indices:
                            filtered_labels.append(labels[idx])
            
            return filtered_labels if filtered_labels else labels
        
        return labels
    
    def extract_time_segment(self, data: np.ndarray, srate: float,
                           start_time: float, end_time: float) -> np.ndarray:
        """
        Extract a time segment from EEG data.
        
        Args:
            data: Full EEG data
            srate: Sampling rate
            start_time: Start time in seconds
            end_time: End time in seconds (-1 for end of data)
            
        Returns:
            Extracted data segment
        """
        start_sample = int(start_time * srate)
        
        if end_time == -1:
            end_sample = data.shape[1] if len(data.shape) > 1 else len(data)
        else:
            end_sample = int(end_time * srate)
        
        # Ensure indices are within bounds
        start_sample = max(0, start_sample)
        if len(data.shape) > 1:
            end_sample = min(data.shape[1], end_sample)
            return data[:, start_sample:end_sample]
        else:
            end_sample = min(len(data), end_sample)
            return data[start_sample:end_sample]
    
    def get_chunk_data(self, data: np.ndarray, chunk_start: int, 
                      chunk_end: int) -> np.ndarray:
        """
        Get a chunk of data for streaming processing.
        
        Args:
            data: Full EEG data
            chunk_start: Start sample index
            chunk_end: End sample index
            
        Returns:
            Data chunk
        """
        if len(data.shape) > 1:
            return data[:, chunk_start:chunk_end]
        else:
            return data[chunk_start:chunk_end]
    
    def validate_data_quality(self, data: np.ndarray) -> Tuple[bool, Optional[str]]:
        """
        Validate EEG data quality.
        
        Args:
            data: EEG data to validate
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        # Check for NaN or Inf values
        if np.any(np.isnan(data)) or np.any(np.isinf(data)):
            return False, "Data contains NaN or Inf values"
        
        # Check for flat signals
        if len(data.shape) > 1:
            for ch in range(data.shape[0]):
                if np.std(data[ch]) < 1e-10:
                    return False, f"Channel {ch+1} has flat signal"
        else:
            if np.std(data) < 1e-10:
                return False, "Signal is flat"
        
        return True, None