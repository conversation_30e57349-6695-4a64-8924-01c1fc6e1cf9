import re
import numpy as np
import logging
from typing import Dict, List, Any, Optional

from core.utils import SamplingRateUtils
from models.parameters import AnalysisParameters
from core.validators import EDFValidator, ParameterValidator, SignalValidator
from core.exceptions.validation_exceptions import ValidationError

from .hfo_detector import HFODetector
from .data_preprocessor import DataPreprocessor
from .chunk_processor import ChunkProcessor
from .result_formatter import ResultFormatter
from .stream_manager import StreamManager
from .chunked_analysis_service import ChunkedEEGStreamProcessor, ChunkedAnalysisService

logger = logging.getLogger(__name__)

class EEGStreamProcessor:
    """Processes EEG files in chunks for real-time streaming"""
    
    def __init__(self, file_path: str, parameters: AnalysisParameters):
        self.file_path = file_path
        self.parameters = parameters
        self.all_results = []
        
        # Initialize validators
        self.edf_validator = EDFValidator()
        self.param_validator = ParameterValidator()
        self.signal_validator = SignalValidator()
        
        # Initialize service components
        self.hfo_detector = HFODetector()
        self.data_preprocessor = DataPreprocessor()
        self.chunk_processor = ChunkProcessor(chunk_duration=10)
        self.result_formatter = ResultFormatter()
        self.stream_manager = StreamManager()
        
    async def initialize(self):
        """Load EDF file header and prepare for processing with validation"""
        try:
            # Validate file first
            is_valid, errors = self.edf_validator.validate_file(self.file_path)
            if not is_valid:
                raise ValidationError(
                    message="EDF file validation failed during initialization",
                    errors=errors
                )
            
            # Load EDF file using preprocessor (full load for stability)
            header, record_data = self.data_preprocessor.load_edf_file(self.file_path)
            
            # Validate header
            is_valid, errors = self.edf_validator.validate_header(header)
            if not is_valid:
                raise ValidationError(
                    message="EDF header validation failed",
                    errors=errors
                )
            
            # Subset channels early based on selection to reduce processing
            if hasattr(self.parameters, 'channel_selection') and self.parameters.channel_selection:
                try:
                    self.data_preprocessor.subset_channels(self.parameters.channel_selection)
                except Exception as e:
                    logger.warning(f"Failed to subset channels: {e}")

            # Validate signal quality for first chunk
            srate = self._get_sampling_rate()
            # Refresh references after potential subsetting
            header = self.data_preprocessor.get_header()
            record_data = self.data_preprocessor.get_record_data()
            if record_data is None or header is None:
                raise ValidationError(message="No EEG data available after subsetting", errors=["record_data is None"]) 
            first_chunk_samples = min(int(10 * srate), record_data.shape[1])
            first_chunk = record_data[:, :first_chunk_samples]
            
            is_valid, errors = self.signal_validator.validate_signal_quality(
                first_chunk, srate, header['label']
            )
            if not is_valid:
                logger.warning(f"Signal quality issues detected: {errors}")
            
            # Run HFO detection on the entire file
            self._run_hfo_detection()
            
            return True
        except ValidationError:
            raise
        except Exception as e:
            logger.error(f"Error initializing processor: {e}")
            raise
    
    def _run_hfo_detection(self):
        """Run the HFO detection algorithm on the entire file"""
        try:
            # Prepare EEG dictionary using preprocessor
            srate = self._get_sampling_rate()
            EEG = self.data_preprocessor.prepare_eeg_dict(srate)
            
            # Prepare montage settings
            montage_type = self._get_montage_type()
            user_ref = ""
            if self.parameters.montage.type == "referential":
                user_ref = self.parameters.montage.reference_channel or ""
            
            # Run HFO detection using detector service
            hfo_results = self.hfo_detector.run_detection(
                EEG,
                self.file_path,
                self.parameters,
                montage_type,
                user_ref
            )
            
            # Process montage data from results
            if hfo_results:
                self.data_preprocessor.process_montage_data(hfo_results)
                
        except Exception as e:
            logger.error(f"Error running HFO detection: {e}")
            import traceback
            logger.error(traceback.format_exc())
    
    def _get_montage_type(self) -> str:
        """Get properly formatted montage type"""
        montage = self.parameters.montage.type
        if montage == "bipolar":
            return "Bipolar"
        elif montage == "average":
            return "Average"
        elif montage == "referential":
            return "Referential"
        return montage
    
    async def process_preview(self) -> Dict[str, Any]:
        """Process first 30 seconds for immediate preview"""
        try:
            srate = self._get_sampling_rate()
            
            # Get montage data or raw data
            data = self.data_preprocessor.get_montage_data()
            if data is None:
                data = self.data_preprocessor.get_record_data()
            
            # Process preview using chunk processor
            preview_result = self.chunk_processor.process_preview(data, srate, 30)
            
            # Count HFOs in preview
            hfo_results = self.hfo_detector.get_results()
            preview_hfo_count = self.result_formatter.count_preview_hfos(
                hfo_results, preview_result['preview_samples'], srate
            )
            
            # Get labels
            labels = self.data_preprocessor.get_montage_labels()
            if labels is None:
                header = self.data_preprocessor.get_header()
                labels = header['label'] if header else []
            
            return {
                "preview_duration": 30,
                "channels": labels,
                "sampling_rate": srate,
                "quick_hfo_count": preview_hfo_count,
                "message": "Preview ready, full analysis continuing..."
            }
            
        except Exception as e:
            logger.error(f"Error processing preview: {e}")
            return {"error": str(e)}
    
    def get_total_chunks(self) -> int:
        """Calculate total number of chunks to process"""
        srate = self._get_sampling_rate()
        data = self.data_preprocessor.get_montage_data()
        if data is None:
            data = self.data_preprocessor.get_record_data()
        
        total_samples = data.shape[1] if data is not None else 0
        return self.chunk_processor.get_total_chunks(total_samples, srate)
    
    async def process_chunk(self, chunk_num: int) -> Dict[str, Any]:
        """Process a specific chunk of the EEG data"""
        try:
            srate = self._get_sampling_rate()
            
            # Get data to use (montage or raw)
            data = self.data_preprocessor.get_montage_data()
            if data is None:
                data = self.data_preprocessor.get_record_data()
            
            if data is None:
                raise ValueError("No data available for processing")
            
            # Extract chunk data using chunk processor
            chunk_data, start_time, end_time = self.chunk_processor.extract_chunk_data(
                data, chunk_num, srate
            )
            
            # Get chunk boundaries for HFO extraction
            start_sample, end_sample = self.chunk_processor.get_chunk_boundaries(
                chunk_num, srate, data.shape[1]
            )
            
            # Extract HFOs for this chunk using result formatter
            hfo_results = self.hfo_detector.get_results()
            chunk_hfos = self.result_formatter.extract_hfos_for_chunk(
                hfo_results, start_sample, end_sample, srate
            )
            
            # Prepare channel data for visualization using preprocessor
            channel_selection = getattr(self.parameters, 'channel_selection', None)
            channel_data = self.data_preprocessor.prepare_channel_data(
                chunk_data, channel_selection
            )
            
            return {
                "time_range": [start_time, end_time],
                "hfo_events": chunk_hfos,
                "channel_data": channel_data,
                "chunk_number": chunk_num
            }
            
        except Exception as e:
            logger.error(f"Error processing chunk {chunk_num}: {e}")
            return {
                "time_range": [0, 0],
                "hfo_events": [],
                "channel_data": {},
                "error": str(e)
            }
    
    def _get_sampling_rate(self) -> float:
        """Extract sampling rate from header"""
        header = self.data_preprocessor.get_header()
        if header is None:
            raise ValueError("No header information available")
        
        freq = header.get('frequency')
        if freq is None:
            raise ValueError("No frequency information found in EDF header")
        return SamplingRateUtils.extract_sampling_rate(freq)
    
    def get_total_hfos(self) -> int:
        """Get total number of HFOs detected"""
        return self.hfo_detector.get_total_hfos()
    
    def get_summary(self) -> Dict[str, Any]:
        """Get analysis summary"""
        # Determine number of channels analyzed
        header = self.data_preprocessor.get_header()
        if header is None:
            return {}
        
        num_channels = len(header['label'])
        if hasattr(self.parameters, 'channel_selection') and self.parameters.channel_selection:
            selected_leads = self.parameters.channel_selection.selected_leads
            if selected_leads:
                # Count channels belonging to selected leads
                selected_count = 0
                for label in header['label']:
                    match = re.match(r'^(?:POL |P )?(\w+?)(\d+)$', label)
                    if match and match.group(1) in selected_leads:
                        selected_count += 1
                    elif label in selected_leads:
                        selected_count += 1
                num_channels = selected_count
        
        # Format summary using result formatter
        frequency_range = f"{self.parameters.frequency.low_cutoff}-{self.parameters.frequency.high_cutoff} Hz"
        return self.result_formatter.format_summary(
            self.get_total_hfos(),
            num_channels,
            frequency_range,
            self.parameters.montage.type,
            len(self.all_results)
        )

class AnalysisService:
    """Service for managing EEG analysis with chunked processing as default"""

    def __init__(self, use_chunked: bool = True):
        """Initialize analysis service

        Args:
            use_chunked: Whether to use chunked processing (default: True for memory optimization)
        """
        self.use_chunked = use_chunked
        self.chunked_service = ChunkedAnalysisService() if use_chunked else None

    async def create_processor(self, file_path: str, parameters: AnalysisParameters,
                             chunk_duration: float = 10.0):
        """Create and initialize an EEG processor

        Args:
            file_path: Path to EDF file
            parameters: Analysis parameters
            chunk_duration: Duration of each chunk in seconds (for chunked processing)

        Returns:
            Either ChunkedEEGStreamProcessor or EEGStreamProcessor based on configuration
        """
        if self.use_chunked:
            # Use chunked processing for memory optimization and progressive HFO detection
            processor = await self.chunked_service.create_processor(
                file_path, parameters, chunk_duration
            )
            return processor
        else:
            # Legacy full-file processing (kept for backward compatibility)
            processor = EEGStreamProcessor(file_path, parameters)
            await processor.initialize()
            return processor