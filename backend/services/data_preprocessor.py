import sys
import re
import numpy as np
import logging
from typing import Dict, List, Any, Optional, Tuple

from models.parameters import ChannelSelection
from core.utils import ChannelLabelUtils

logger = logging.getLogger(__name__)

# Add hfo_engine to path
sys.path.append('./core/hfo_engine')

from pyedfreader import edfread
from browse_files import extract_edf_metadata

class DataPreprocessor:
    """Service for EDF data preprocessing and montage processing"""
    
    def __init__(self):
        self.header = None
        self.record_data = None
        self.montage_data = None
        self.montage_labels = None
        
    def load_edf_file(self, file_path: str, target_labels: Optional[List[str]] = None) -> Tuple[Dict[str, Any], np.ndarray]:
        """Load EDF file and return header and data.
        Note: For stability, we currently load all channels and subset later.
        """
        # Load header information first (metadata-only pass)
        self.header = extract_edf_metadata(file_path)
        
        # Load full data (targeted reading disabled due to EDF reader limitations)
        self.header, self.record_data = edfread(file_path)
        
        # Process channel labels
        self._process_header_labels()
        
        return self.header, self.record_data
    
    def _process_header_labels(self):
        """Clean and process header labels"""
        for idx, label in enumerate(self.header['label']):
            channel_id = re.search(r'\d+', label)
            channel_name = re.sub(r'^(POL )|(P )|(\d+)|( )', '', label)
            if channel_id and channel_name:
                self.header['label'][idx] = f"{channel_name}{channel_id.group()}"
            elif channel_name:
                self.header['label'][idx] = channel_name
    
    def prepare_eeg_dict(self, sampling_rate: float) -> Dict[str, Any]:
        """Prepare EEG dictionary for the HFO algorithm"""
        return {
            'data': self.record_data,
            'nbchan': len(self.header['label']),
            'srate': sampling_rate,
            'chanlocs': self.header['label'],
            'records': self.header.get('records', 1),
            'duration': self.header.get('duration', 1)
        }
    
    def process_montage_data(self, hfo_results: Dict[str, Any]):
        """Store montage-processed data from HFO results"""
        if hfo_results and hfo_results.get('success'):
            # Store montage-processed data and labels
            if 'myEEG' in hfo_results:
                self.montage_data = hfo_results['myEEG']
                logger.info(f"Stored montage data with shape: {self.montage_data.shape}")
            if 'channel_labels' in hfo_results:
                self.montage_labels = hfo_results['channel_labels']
                logger.info(f"Stored montage labels: {self.montage_labels[:5]}..." 
                          if len(self.montage_labels) > 5 
                          else f"Stored montage labels: {self.montage_labels}")
    
    def prepare_channel_data(self, chunk_data: np.ndarray, 
                           channel_selection: Optional[Any] = None,
                           max_points: int = 1000) -> Dict[str, List[float]]:
        """Prepare channel data for visualization"""
        channel_data = {}
        
        # Downsample if necessary
        if chunk_data.shape[1] > max_points:
            downsample_factor = chunk_data.shape[1] // max_points
            downsampled = chunk_data[:, ::downsample_factor]
        else:
            downsampled = chunk_data
        
        # Use montage labels if available, otherwise use original labels
        labels_to_use = self.montage_labels if self.montage_labels else self.header['label']
        
        # Get selected leads from channel_selection (normalized)
        selected_leads: List[str] = []
        if channel_selection and hasattr(channel_selection, 'selected_leads'):
            raw_selected = channel_selection.selected_leads
            selected_leads = [ChannelLabelUtils.clean_channel_label(s) for s in raw_selected]
        
        # Make sure we don't exceed the number of channels in the data
        num_channels = min(len(labels_to_use), downsampled.shape[0])
        
        for i in range(num_channels):
            label = labels_to_use[i]
            if selected_leads:
                # For montage labels like "RG1-RG2", include if any component or base lead is selected
                bipolar_match = re.match(r'^(\w+?)(\d+)-(\w+?)(\d+)$', label)
                if bipolar_match:
                    lead1 = bipolar_match.group(1)
                    num1 = bipolar_match.group(2)
                    lead2 = bipolar_match.group(3)
                    num2 = bipolar_match.group(4)
                    comp1 = f"{lead1}{num1}"
                    comp2 = f"{lead2}{num2}"
                    if (
                        lead1 in selected_leads or lead2 in selected_leads or
                        comp1 in selected_leads or comp2 in selected_leads or
                        label in selected_leads
                    ):
                        channel_data[label] = downsampled[i].tolist()
                else:
                    # Check base lead or exact label match
                    match = re.match(r'^(?:POL |P )?(\w+?)(\d+)', label)
                    if match:
                        lead_name = match.group(1)
                        if lead_name in selected_leads or ChannelLabelUtils.clean_channel_label(label) in selected_leads:
                            channel_data[label] = downsampled[i].tolist()
                    elif ChannelLabelUtils.clean_channel_label(label) in selected_leads or label in selected_leads:
                        channel_data[label] = downsampled[i].tolist()
            else:
                # If no specific channels selected, include all
                channel_data[label] = downsampled[i].tolist()
        
        return channel_data

    def subset_channels(self, selection: Optional[ChannelSelection]) -> None:
        """Subset loaded header/record_data to only selected leads/contacts.
        If selection is None or empty, keep all channels.
        """
        if self.header is None or self.record_data is None:
            return
        if selection is None:
            return
        selected_leads = selection.selected_leads if hasattr(selection, 'selected_leads') else []
        contact_specs = selection.contact_specifications if hasattr(selection, 'contact_specifications') else {}
        if not selected_leads and not contact_specs:
            return

        labels = list(self.header['label'])
        groups = ChannelLabelUtils.extract_channel_groups(labels)

        keep_indices: List[int] = []
        # Include exact label matches first
        label_to_index = {label: i for i, label in enumerate(labels)}
        for item in selected_leads:
            if item in label_to_index:
                keep_indices.append(label_to_index[item])
        
        # Include all channels for selected lead groups (e.g., 'RG')
        for lead in selected_leads:
            if lead in groups:
                keep_indices.extend(groups[lead])

        # Apply contact specifications per lead
        for lead, spec in contact_specs.items():
            if lead in groups and spec:
                contacts = ChannelLabelUtils.parse_contact_specification(spec)
                for idx in groups[lead]:
                    # contacts are 1-based; convert from label number if possible
                    num = ChannelLabelUtils.extract_channel_number(labels[idx])
                    if num is not None and num in contacts:
                        keep_indices.append(idx)

        if not keep_indices:
            return

        keep_indices = sorted(set(keep_indices))
        self.record_data = self.record_data[keep_indices, :]
        self.header['label'] = [labels[i] for i in keep_indices]
        self.header['ns'] = len(keep_indices)
    
    def get_header(self) -> Optional[Dict[str, Any]]:
        """Get EDF header"""
        return self.header
    
    def get_record_data(self) -> Optional[np.ndarray]:
        """Get raw EDF data"""
        return self.record_data
    
    def get_montage_data(self) -> Optional[np.ndarray]:
        """Get montage-processed data"""
        return self.montage_data
    
    def get_montage_labels(self) -> Optional[List[str]]:
        """Get montage channel labels"""
        return self.montage_labels