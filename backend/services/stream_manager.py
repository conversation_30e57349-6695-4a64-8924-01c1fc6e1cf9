import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class StreamManager:
    """Service for managing WebSocket streaming and real-time communication"""
    
    def __init__(self):
        self.active_streams = {}
        
    def prepare_chunk_response(self, chunk_data: Dict[str, Any],
                               chunk_num: int, total_chunks: int) -> Dict[str, Any]:
        """Prepare chunk data for WebSocket transmission"""
        return {
            "type": "chunk_data",
            "chunk_number": chunk_num,
            "total_chunks": total_chunks,
            "progress": (chunk_num + 1) / total_chunks * 100,
            "data": chunk_data
        }
    
    def prepare_preview_response(self, preview_data: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare preview data for WebSocket transmission"""
        return {
            "type": "preview_data",
            "data": preview_data
        }
    
    def prepare_error_response(self, error_message: str, 
                              error_type: str = "general") -> Dict[str, Any]:
        """Prepare error response for WebSocket transmission"""
        return {
            "type": "error",
            "error_type": error_type,
            "message": error_message
        }
    
    def prepare_progress_update(self, current_step: str, 
                               progress_percentage: float,
                               message: Optional[str] = None) -> Dict[str, Any]:
        """Prepare progress update for WebSocket transmission"""
        update = {
            "type": "progress_update",
            "step": current_step,
            "progress": progress_percentage
        }
        if message:
            update["message"] = message
        return update
    
    def prepare_analysis_complete(self, summary: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare analysis completion message"""
        return {
            "type": "analysis_complete",
            "summary": summary
        }
    
    def register_stream(self, stream_id: str, metadata: Dict[str, Any]):
        """Register a new WebSocket stream"""
        self.active_streams[stream_id] = metadata
        logger.info(f"Registered stream: {stream_id}")
    
    def unregister_stream(self, stream_id: str):
        """Unregister a WebSocket stream"""
        if stream_id in self.active_streams:
            del self.active_streams[stream_id]
            logger.info(f"Unregistered stream: {stream_id}")
    
    def get_active_streams_count(self) -> int:
        """Get count of active streams"""
        return len(self.active_streams)