"""
Base service class for analysis services.
Provides common functionality for all analysis services.
"""

import logging
from typing import Dict, Any, Optional
from abc import ABC, abstractmethod


class BaseAnalysisService(ABC):
    """Base class for analysis services"""
    
    def __init__(self):
        """Initialize the base service"""
        self.logger = logging.getLogger(self.__class__.__name__)
        self._is_initialized = False
        self._config = {}
    
    def initialize(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the service with configuration.
        
        Args:
            config: Optional configuration dictionary
        """
        if config:
            self._config = config
        self._is_initialized = True
        self.logger.info(f"{self.__class__.__name__} initialized")
    
    def is_initialized(self) -> bool:
        """Check if service is initialized"""
        return self._is_initialized
    
    @abstractmethod
    def validate_input(self, data: Any) -> bool:
        """
        Validate input data.
        
        Args:
            data: Input data to validate
            
        Returns:
            bool: True if valid, False otherwise
        """
        pass
    
    @abstractmethod
    def process(self, data: Any) -> Any:
        """
        Process the input data.
        
        Args:
            data: Data to process
            
        Returns:
            Processed result
        """
        pass
    
    def log_error(self, error: Exception, context: Optional[str] = None):
        """
        Log an error with context.
        
        Args:
            error: The exception to log
            context: Optional context information
        """
        if context:
            self.logger.error(f"{context}: {str(error)}")
        else:
            self.logger.error(str(error))
    
    def log_warning(self, message: str):
        """Log a warning message"""
        self.logger.warning(message)
    
    def log_info(self, message: str):
        """Log an info message"""
        self.logger.info(message)
    
    def log_debug(self, message: str):
        """Log a debug message"""
        self.logger.debug(message)