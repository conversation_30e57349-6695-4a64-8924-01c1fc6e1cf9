import sys
import re
import numpy as np
import logging
from typing import Dict, List, Any, Optional, Tuple, Iterator

from models.parameters import ChannelSelection
from core.utils import ChannelLabelUtils

logger = logging.getLogger(__name__)

# Add hfo_engine to path
sys.path.append('./core/hfo_engine')

from chunked_edf_reader import ChunkedEDFReader
from browse_files import extract_edf_metadata


class ChunkedDataPreprocessor:
    """Service for chunked EDF data preprocessing with streaming support"""
    
    def __init__(self, chunk_duration: float = 10.0):
        self.chunk_duration = chunk_duration
        self.reader = None
        self.header = None
        self.selected_channel_indices = None
        self.channel_selection = None
        
    def initialize(self, file_path: str) -> Dict[str, Any]:
        """Initialize the preprocessor with file metadata without loading all data"""
        try:
            # Load header information first (metadata-only pass)
            self.header = extract_edf_metadata(file_path)
            
            # Create chunked reader
            self.reader = ChunkedEDFReader(file_path, self.chunk_duration)
            self.reader.open()
            
            # Get full header from chunked reader (more detailed)
            reader_header = self.reader.get_header()
            if reader_header:
                self.header.update(reader_header)
                
            # Process channel labels
            self._process_header_labels()
            
            logger.info(f"Initialized chunked preprocessor for {file_path}")
            logger.info(f"File has {self.header['ns']} channels, "
                       f"{self.header['records']} records, "
                       f"duration: {self.header['records'] * self.header['duration']}s")
            
            return self.header
            
        except Exception as e:
            logger.error(f"Error initializing chunked preprocessor: {e}")
            raise
            
    def _process_header_labels(self):
        """Clean and process header labels"""
        for idx, label in enumerate(self.header['label']):
            channel_id = re.search(r'\d+', label)
            channel_name = re.sub(r'^(POL )|(P )|(\d+)|( )', '', label)
            if channel_id and channel_name:
                self.header['label'][idx] = f"{channel_name}{channel_id.group()}"
            elif channel_name:
                self.header['label'][idx] = channel_name
                
    def subset_channels(self, selection: Optional[ChannelSelection]) -> None:
        """Configure channel selection for chunked reading"""
        if selection is None:
            self.selected_channel_indices = None
            return
            
        self.channel_selection = selection
        selected_leads = selection.selected_leads if hasattr(selection, 'selected_leads') else []
        contact_specs = selection.contact_specifications if hasattr(selection, 'contact_specifications') else {}
        
        if not selected_leads and not contact_specs:
            self.selected_channel_indices = None
            return
            
        labels = list(self.header['label'])
        groups = ChannelLabelUtils.extract_channel_groups(labels)
        
        keep_indices: List[int] = []
        
        # Include exact label matches first
        label_to_index = {label: i for i, label in enumerate(labels)}
        for item in selected_leads:
            if item in label_to_index:
                keep_indices.append(label_to_index[item])
                
        # Include all channels for selected lead groups
        for lead in selected_leads:
            if lead in groups:
                keep_indices.extend(groups[lead])
                
        # Apply contact specifications
        for lead, spec in contact_specs.items():
            if lead in groups and spec:
                contacts = ChannelLabelUtils.parse_contact_specification(spec)
                for idx in groups[lead]:
                    num = ChannelLabelUtils.extract_channel_number(labels[idx])
                    if num is not None and num in contacts:
                        keep_indices.append(idx)
                        
        if keep_indices:
            self.selected_channel_indices = sorted(set(keep_indices))
            # Update reader with selected channels
            if self.reader:
                self.reader.select_channels(self.selected_channel_indices)
            logger.info(f"Selected {len(self.selected_channel_indices)} channels for processing")
        else:
            self.selected_channel_indices = None
            
    def iter_chunks(self, start_time: float = 0, 
                   end_time: Optional[float] = None) -> Iterator[Tuple[np.ndarray, float, float]]:
        """
        Iterate over chunks of EDF data
        
        Yields:
            Tuple of (chunk_data, chunk_start_time, chunk_end_time)
        """
        if not self.reader:
            raise RuntimeError("Preprocessor not initialized")
            
        # Apply channel selection if configured
        if self.selected_channel_indices is not None:
            selected_labels = [self.header['label'][i] for i in self.selected_channel_indices]
        else:
            selected_labels = self.header['label']
            
        # Iterate through chunks
        for chunk_data, start, end in self.reader.iter_chunks(start_time, end_time):
            # Log chunk info
            logger.debug(f"Processing chunk: {start:.1f}s - {end:.1f}s, "
                        f"shape: {chunk_data.shape}")
            
            yield chunk_data, start, end
            
    def get_chunk(self, chunk_index: int) -> Tuple[np.ndarray, float, float]:
        """
        Get a specific chunk by index
        
        Args:
            chunk_index: Index of the chunk to retrieve
            
        Returns:
            Tuple of (chunk_data, chunk_start_time, chunk_end_time)
        """
        start_time = chunk_index * self.chunk_duration
        end_time = start_time + self.chunk_duration
        
        # Get chunk using iterator
        for i, (data, start, end) in enumerate(self.iter_chunks(start_time, end_time)):
            if i == 0:  # First (and only) chunk in range
                return data, start, end
                
        # If no chunk found, return empty
        return np.array([]), start_time, end_time
        
    def get_total_chunks(self) -> int:
        """Calculate total number of chunks in the file"""
        if not self.header:
            return 0
            
        total_duration = self.header['records'] * self.header['duration']
        return int(np.ceil(total_duration / self.chunk_duration))
        
    def prepare_chunk_eeg_dict(self, chunk_data: np.ndarray, 
                              chunk_start_time: float) -> Dict[str, Any]:
        """Prepare EEG dictionary for a chunk for the HFO algorithm"""
        sampling_rate = self.header['frequency'][0] if isinstance(self.header['frequency'], np.ndarray) else self.header['frequency']
        
        # Get appropriate labels
        if self.selected_channel_indices is not None:
            labels = [self.header['label'][i] for i in self.selected_channel_indices]
        else:
            labels = self.header['label']
            
        return {
            'data': chunk_data,
            'nbchan': chunk_data.shape[0],
            'srate': sampling_rate,
            'chanlocs': labels,
            'chunk_start_time': chunk_start_time,
            'chunk_samples': chunk_data.shape[1]
        }
        
    def prepare_channel_data(self, chunk_data: np.ndarray,
                           max_points: int = 1000) -> Dict[str, List[float]]:
        """Prepare channel data for visualization"""
        channel_data = {}
        
        # Downsample if necessary
        if chunk_data.shape[1] > max_points:
            downsample_factor = chunk_data.shape[1] // max_points
            downsampled = chunk_data[:, ::downsample_factor]
        else:
            downsampled = chunk_data
            
        # Get appropriate labels
        if self.selected_channel_indices is not None:
            labels = [self.header['label'][i] for i in self.selected_channel_indices]
        else:
            labels = self.header['label']
            
        # Make sure we don't exceed the number of channels
        num_channels = min(len(labels), downsampled.shape[0])
        
        # Process based on channel selection
        if self.channel_selection and hasattr(self.channel_selection, 'selected_leads'):
            selected_leads = [ChannelLabelUtils.clean_channel_label(s) 
                            for s in self.channel_selection.selected_leads]
        else:
            selected_leads = []
            
        for i in range(num_channels):
            label = labels[i]
            
            if selected_leads:
                # Check if channel should be included
                match = re.match(r'^(?:POL |P )?(\w+?)(\d+)', label)
                if match:
                    lead_name = match.group(1)
                    if (lead_name in selected_leads or 
                        ChannelLabelUtils.clean_channel_label(label) in selected_leads):
                        channel_data[label] = downsampled[i].tolist()
                elif ChannelLabelUtils.clean_channel_label(label) in selected_leads or label in selected_leads:
                    channel_data[label] = downsampled[i].tolist()
            else:
                # Include all channels
                channel_data[label] = downsampled[i].tolist()
                
        return channel_data
        
    def get_header(self) -> Optional[Dict[str, Any]]:
        """Get EDF header"""
        return self.header
        
    def close(self):
        """Close the EDF file reader"""
        if self.reader:
            self.reader.close()
            self.reader = None
            
    def __enter__(self):
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
