import sys
import numpy as np
import logging
from typing import Dict, List, Any, Optional, Tuple, Iterator

logger = logging.getLogger(__name__)

# Add hfo_engine to path
sys.path.append('./core/hfo_engine')

from pyedfreader import edfread
from browse_files import extract_edf_metadata


class ChunkedEDFReader:
    """Chunked EDF file reader for memory-efficient processing"""

    def __init__(self, file_path: str, chunk_duration: float = 10.0):
        self.file_path = file_path
        self.chunk_duration = chunk_duration
        self.header = None
        self.is_open = False
        self.sampling_rate = None
        self.total_samples = None
        self.chunk_samples = None
        self.total_chunks = None

    def open(self):
        """Open the EDF file and read header"""
        try:
            # Load header only
            self.header = extract_edf_metadata(self.file_path)

            # Get sampling rate (assuming uniform across channels)
            if 'srate' in self.header:
                self.sampling_rate = self.header['srate']
            elif 'fs' in self.header:
                self.sampling_rate = self.header['fs']
            else:
                # Fallback: calculate from header info
                self.sampling_rate = self.header.get('records', 1) * self.header.get('duration', 1)

            # Calculate chunk parameters
            self.chunk_samples = int(self.chunk_duration * self.sampling_rate)

            # Estimate total samples (will be refined when we load data)
            self.total_samples = int(self.header.get('records', 1) * self.header.get('duration', 1) * self.sampling_rate)
            self.total_chunks = int(np.ceil(self.total_samples / self.chunk_samples))

            self.is_open = True
            logger.info(f"Opened EDF file: {self.total_chunks} chunks of {self.chunk_duration}s each")

        except Exception as e:
            logger.error(f"Error opening EDF file: {e}")
            raise

    def get_header(self) -> Dict[str, Any]:
        """Get file header"""
        return self.header

    def get_chunk(self, chunk_index: int) -> Tuple[np.ndarray, float, float]:
        """Get a specific chunk of data"""
        if not self.is_open:
            raise RuntimeError("File not opened")

        # Calculate time range for this chunk
        start_time = chunk_index * self.chunk_duration
        end_time = min((chunk_index + 1) * self.chunk_duration,
                      self.total_samples / self.sampling_rate)

        # For now, load entire file and extract chunk (can be optimized later)
        # This is a simplified implementation
        try:
            header, data = edfread(self.file_path)

            # Calculate sample indices
            start_sample = int(start_time * self.sampling_rate)
            end_sample = min(int(end_time * self.sampling_rate), data.shape[1])

            # Extract chunk
            chunk_data = data[:, start_sample:end_sample]

            return chunk_data, start_time, end_time

        except Exception as e:
            logger.error(f"Error reading chunk {chunk_index}: {e}")
            raise

    def iter_chunks(self, start_time: float = 0,
                   end_time: Optional[float] = None) -> Iterator[Tuple[np.ndarray, float, float]]:
        """Iterate over chunks"""
        if not self.is_open:
            raise RuntimeError("File not opened")

        start_chunk = int(start_time / self.chunk_duration)
        if end_time is None:
            end_chunk = self.total_chunks
        else:
            end_chunk = min(int(np.ceil(end_time / self.chunk_duration)), self.total_chunks)

        for chunk_idx in range(start_chunk, end_chunk):
            yield self.get_chunk(chunk_idx)

    def close(self):
        """Close the file"""
        self.is_open = False


class ChunkedDataPreprocessor:
    """Service for chunked EDF data preprocessing"""

    def __init__(self, chunk_duration: float = 10.0):
        self.chunk_duration = chunk_duration
        self.header = None
        self.reader = None
        self.selected_channel_indices = None

        # Performance optimizations
        self.adaptive_chunk_size = True
        self.min_chunk_duration = 5.0  # Minimum chunk size
        self.max_chunk_duration = 15.0  # Maximum chunk size

    def initialize(self, file_path: str) -> Dict[str, Any]:
        """Initialize the preprocessor with file metadata without loading all data"""
        try:
            # Load header information first (metadata-only pass)
            self.header = extract_edf_metadata(file_path)

            # Optimize chunk duration based on sampling rate
            if self.adaptive_chunk_size and 'srate' in self.header:
                sampling_rate = self.header['srate']
                # Adjust chunk size based on sampling rate for optimal performance
                if sampling_rate > 2000:  # High sampling rate
                    self.chunk_duration = max(self.min_chunk_duration, self.chunk_duration * 0.7)
                elif sampling_rate < 500:  # Low sampling rate
                    self.chunk_duration = min(self.max_chunk_duration, self.chunk_duration * 1.3)

                logger.info(f"Adaptive chunk duration: {self.chunk_duration:.1f}s for {sampling_rate}Hz sampling rate")

            # Create chunked reader
            self.reader = ChunkedEDFReader(file_path, self.chunk_duration)
            self.reader.open()

            # Get full header from chunked reader (more detailed)
            reader_header = self.reader.get_header()
            if reader_header:
                self.header.update(reader_header)

            # Process channel labels
            self._process_header_labels()

            logger.info(f"Initialized chunked preprocessor for {file_path}")
            return self.header

        except Exception as e:
            logger.error(f"Error initializing chunked preprocessor: {e}")
            raise

    def _process_header_labels(self):
        """Clean and process header labels"""
        if 'label' in self.header:
            import re
            for idx, label in enumerate(self.header['label']):
                channel_id = re.search(r'\d+', label)
                channel_name = re.sub(r'^(POL )|(P )|(\d+)|( )', '', label)
                if channel_id and channel_name:
                    self.header['label'][idx] = f"{channel_name}{channel_id.group()}"
                elif channel_name:
                    self.header['label'][idx] = channel_name

    def get_chunk(self, chunk_index: int) -> Tuple[np.ndarray, float, float]:
        """Get a specific chunk of data"""
        if not self.reader:
            raise RuntimeError("Preprocessor not initialized")

        chunk_data, start_time, end_time = self.reader.get_chunk(chunk_index)

        # Apply channel selection if configured
        if self.selected_channel_indices is not None:
            chunk_data = chunk_data[self.selected_channel_indices, :]

        return chunk_data, start_time, end_time

    def get_total_chunks(self) -> int:
        """Get total number of chunks"""
        if not self.reader:
            raise RuntimeError("Preprocessor not initialized")
        return self.reader.total_chunks

    def iter_chunks(self, start_time: float = 0,
                   end_time: Optional[float] = None) -> Iterator[Tuple[np.ndarray, float, float]]:
        """
        Iterate over chunks of EDF data

        Yields:
            Tuple of (chunk_data, chunk_start_time, chunk_end_time)
        """
        if not self.reader:
            raise RuntimeError("Preprocessor not initialized")

        # Apply channel selection if configured
        if self.selected_channel_indices is not None:
            selected_labels = [self.header['label'][i] for i in self.selected_channel_indices]
        else:
            selected_labels = self.header['label']

        # Iterate through chunks
        for chunk_data, start, end in self.reader.iter_chunks(start_time, end_time):
            # Log chunk info
            logger.debug(f"Processing chunk: {start:.1f}s - {end:.1f}s, "
                        f"shape: {chunk_data.shape}")

            yield chunk_data, start, end

    def subset_channels(self, channel_selection: Any):
        """Apply channel selection without loading full file"""
        try:
            if not hasattr(channel_selection, 'selected_leads') or not channel_selection.selected_leads:
                return

            # Get available channel labels
            labels = self.header.get('label', [])
            if not labels:
                return

            # Find indices of selected channels
            selected_indices = []
            for lead in channel_selection.selected_leads:
                for idx, label in enumerate(labels):
                    if lead.lower() in label.lower():
                        selected_indices.append(idx)
                        break

            if selected_indices:
                self.selected_channel_indices = selected_indices
                logger.info(f"Selected {len(selected_indices)} channels for processing")

        except Exception as e:
            logger.warning(f"Error applying channel selection: {e}")

    def prepare_channel_data(self, chunk_data: np.ndarray,
                           max_points: int = 1000) -> Dict[str, List[float]]:
        """Prepare channel data for visualization"""
        channel_data = {}

        # Downsample if necessary
        if chunk_data.shape[1] > max_points:
            downsample_factor = chunk_data.shape[1] // max_points
            downsampled = chunk_data[:, ::downsample_factor]
        else:
            downsampled = chunk_data

        # Get appropriate labels
        if self.selected_channel_indices is not None:
            labels = [self.header['label'][i] for i in self.selected_channel_indices]
        else:
            labels = self.header['label']

        # Convert to dictionary format
        for i, label in enumerate(labels):
            if i < downsampled.shape[0]:
                channel_data[label] = downsampled[i, :].tolist()

        return channel_data

    def close(self):
        """Clean up resources"""
        if self.reader:
            self.reader.close()

    def get_memory_usage_estimate(self) -> Dict[str, float]:
        """Estimate current memory usage in MB"""
        import sys

        # Estimate header size
        header_size = sys.getsizeof(self.header) if self.header else 0

        # Estimate reader overhead
        reader_size = sys.getsizeof(self.reader) if self.reader else 0

        return {
            'header_mb': header_size / (1024 * 1024),
            'reader_mb': reader_size / (1024 * 1024),
            'total_mb': (header_size + reader_size) / (1024 * 1024)
        }