import numpy as np
import logging
import re
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)

class ResultFormatter:
    """Service for formatting HFO results for API responses"""
    
    def extract_hfos_for_chunk(self, hfo_results: Dict[str, Any],
                               start_sample: int, end_sample: int, 
                               srate: float) -> List[Dict[str, Any]]:
        """Extract HFOs that fall within the given sample range"""
        chunk_hfos = []
        
        if not hfo_results:
            return chunk_hfos
        
        try:
            channel_labels = hfo_results.get('channel_labels', [])
            
            # Process accepted HFOs (final_start_ind/final_end_ind)
            chunk_hfos.extend(self._extract_accepted_hfos(
                hfo_results, channel_labels, start_sample, end_sample, srate
            ))
            
            # Process rejected HFOs (rejected_start_ind/rejected_end_ind)
            chunk_hfos.extend(self._extract_rejected_hfos(
                hfo_results, channel_labels, start_sample, end_sample, srate
            ))
            
            # Process rejected long HFOs (rejecLONG_start_ind/rejecLONG_end_ind)
            chunk_hfos.extend(self._extract_rejected_long_hfos(
                hfo_results, channel_labels, start_sample, end_sample, srate
            ))
            
            # Log HFO counts
            self._log_chunk_hfo_counts(chunk_hfos, start_sample, end_sample)
            
        except Exception as e:
            logger.error(f"Error extracting HFOs for chunk: {e}")
        
        return chunk_hfos
    
    def _extract_accepted_hfos(self, hfo_results: Dict[str, Any], 
                               channel_labels: List[str],
                               start_sample: int, end_sample: int, 
                               srate: float) -> List[Dict[str, Any]]:
        """Extract accepted HFOs for chunk"""
        chunk_hfos = []
        final_start = hfo_results.get('final_start_ind', [])
        final_end = hfo_results.get('final_end_ind', [])
        
        # Get optional HFO characteristics if available
        durations = hfo_results.get('duration', [])
        peak_freqs = hfo_results.get('peak_freq', [])
        hfo_powers = hfo_results.get('hfo_power', [])
        amplitudes = hfo_results.get('my_amp', [])
        
        # Check if final_start is a list of numpy arrays
        if final_start and hasattr(final_start[0], '__len__') and not isinstance(final_start[0], str):
            for chan_idx, channel_starts in enumerate(final_start):
                if chan_idx >= len(final_end) or chan_idx >= len(channel_labels):
                    continue
                    
                channel_ends = final_end[chan_idx]
                channel_name = channel_labels[chan_idx]
                
                # Process each HFO in this channel
                for hfo_idx, hfo_start in enumerate(channel_starts):
                    if hfo_start == 0:  # Skip zero entries (padding)
                        continue
                        
                    hfo_end = channel_ends[hfo_idx] if hfo_idx < len(channel_ends) else hfo_start
                    
                    # Check if HFO is within this chunk
                    if hfo_start >= start_sample and hfo_start < end_sample:
                        hfo_event = self._create_hfo_event(
                            "accepted", hfo_start, hfo_end, channel_name,
                            start_sample, srate
                        )
                        
                        # Add optional characteristics
                        self._add_hfo_characteristics(
                            hfo_event, chan_idx, hfo_idx,
                            durations, peak_freqs, hfo_powers, amplitudes
                        )
                        
                        chunk_hfos.append(hfo_event)
        
        return chunk_hfos
    
    def _extract_rejected_hfos(self, hfo_results: Dict[str, Any],
                               channel_labels: List[str],
                               start_sample: int, end_sample: int,
                               srate: float) -> List[Dict[str, Any]]:
        """Extract rejected HFOs for chunk"""
        chunk_hfos = []
        rejected_start = hfo_results.get('rejected_start_ind', [])
        rejected_end = hfo_results.get('rejected_end_ind', [])
        
        # Handle numpy arrays properly
        if hasattr(rejected_start, 'shape') and len(rejected_start.shape) == 2:
            for chan_idx in range(rejected_start.shape[0]):
                if chan_idx >= len(channel_labels):
                    continue
                
                channel_name = channel_labels[chan_idx]
                channel_starts = rejected_start[chan_idx]
                channel_ends = rejected_end[chan_idx] if chan_idx < rejected_end.shape[0] else channel_starts
                
                for hfo_idx in range(len(channel_starts)):
                    hfo_start = channel_starts[hfo_idx]
                    if hfo_start == 0:  # Skip zero entries
                        continue
                        
                    hfo_end = channel_ends[hfo_idx] if hfo_idx < len(channel_ends) else hfo_start
                    
                    if hfo_start >= start_sample and hfo_start < end_sample:
                        chunk_hfos.append(self._create_hfo_event(
                            "rejected", hfo_start, hfo_end, channel_name,
                            start_sample, srate
                        ))
        
        return chunk_hfos
    
    def _extract_rejected_long_hfos(self, hfo_results: Dict[str, Any],
                                    channel_labels: List[str],
                                    start_sample: int, end_sample: int,
                                    srate: float) -> List[Dict[str, Any]]:
        """Extract rejected long HFOs for chunk"""
        chunk_hfos = []
        rejected_long_start = hfo_results.get('rejecLONG_start_ind', [])
        rejected_long_end = hfo_results.get('rejecLONG_end_ind', [])
        
        # Handle numpy arrays properly
        if hasattr(rejected_long_start, 'shape') and len(rejected_long_start.shape) == 2:
            for chan_idx in range(rejected_long_start.shape[0]):
                if chan_idx >= len(channel_labels):
                    continue
                
                channel_name = channel_labels[chan_idx]
                channel_starts = rejected_long_start[chan_idx]
                channel_ends = rejected_long_end[chan_idx] if chan_idx < rejected_long_end.shape[0] else channel_starts
                
                for hfo_idx in range(len(channel_starts)):
                    hfo_start = channel_starts[hfo_idx]
                    if hfo_start == 0:  # Skip zero entries
                        continue
                        
                    hfo_end = channel_ends[hfo_idx] if hfo_idx < len(channel_ends) else hfo_start
                    
                    if hfo_start >= start_sample and hfo_start < end_sample:
                        chunk_hfos.append(self._create_hfo_event(
                            "rejected_long", hfo_start, hfo_end, channel_name,
                            start_sample, srate
                        ))
        
        return chunk_hfos
    
    def _create_hfo_event(self, event_type: str, hfo_start: int, hfo_end: int,
                         channel_name: str, start_sample: int, srate: float) -> Dict[str, Any]:
        """Create HFO event dictionary"""
        return {
            "type": event_type,
            "start_time": hfo_start / srate,  # Absolute time from file start
            "end_time": hfo_end / srate,      # Absolute time from file start
            "channel": channel_name,
            "sample_start": int(hfo_start - start_sample),
            "sample_end": int(hfo_end - start_sample)
        }
    
    def _add_hfo_characteristics(self, hfo_event: Dict[str, Any], 
                                 chan_idx: int, hfo_idx: int,
                                 durations: List, peak_freqs: List,
                                 hfo_powers: List, amplitudes: List):
        """Add optional HFO characteristics to event"""
        if chan_idx < len(durations) and hfo_idx < len(durations[chan_idx]):
            hfo_event["duration_ms"] = durations[chan_idx][hfo_idx]
        if chan_idx < len(peak_freqs) and hfo_idx < len(peak_freqs[chan_idx]):
            hfo_event["peak_frequency"] = peak_freqs[chan_idx][hfo_idx]
        if chan_idx < len(hfo_powers) and hfo_idx < len(hfo_powers[chan_idx]):
            hfo_event["power"] = hfo_powers[chan_idx][hfo_idx]
        if chan_idx < len(amplitudes) and hfo_idx < len(amplitudes[chan_idx]):
            hfo_event["amplitude"] = amplitudes[chan_idx][hfo_idx]
    
    def _log_chunk_hfo_counts(self, chunk_hfos: List[Dict[str, Any]], 
                             start_sample: int, end_sample: int):
        """Log HFO counts for debugging"""
        if len(chunk_hfos) > 0:
            accepted = sum(1 for h in chunk_hfos if h['type'] == 'accepted')
            rejected = sum(1 for h in chunk_hfos if h['type'] == 'rejected')
            rejected_long = sum(1 for h in chunk_hfos if h['type'] == 'rejected_long')
            
            logger.info(f"Chunk samples {start_sample}-{end_sample}: "
                       f"Found {len(chunk_hfos)} HFOs "
                       f"(accepted: {accepted}, rejected: {rejected}, rejected_long: {rejected_long})")
    
    def format_summary(self, total_hfos: int, num_channels: int,
                      frequency_range: str, montage_type: str,
                      chunks_processed: int = 0) -> Dict[str, Any]:
        """Format analysis summary"""
        return {
            "total_hfos": total_hfos,
            "total_chunks_processed": chunks_processed,
            "channels_analyzed": num_channels,
            "frequency_range": frequency_range,
            "montage": montage_type
        }
    
    def count_preview_hfos(self, hfo_results: Dict[str, Any], 
                          preview_samples: int, srate: float) -> int:
        """Count HFOs in preview duration"""
        preview_hfo_count = 0
        if hfo_results and 'final_start_ind' in hfo_results:
            final_start = hfo_results['final_start_ind']
            if hasattr(final_start[0], '__len__'):
                for channel_starts in final_start:
                    for hfo_start in channel_starts:
                        if hfo_start > 0 and hfo_start < preview_samples:
                            preview_hfo_count += 1
        return preview_hfo_count