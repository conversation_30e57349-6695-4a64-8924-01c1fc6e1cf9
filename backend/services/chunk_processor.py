import numpy as np
import logging
from typing import Dict, Any, Tuple

logger = logging.getLogger(__name__)

class ChunkProcessor:
    """Service for processing EEG data in chunks"""
    
    def __init__(self, chunk_duration: int = 10):
        self.chunk_duration = chunk_duration  # Default 10-second chunks
        
    def get_total_chunks(self, total_samples: int, sampling_rate: float) -> int:
        """Calculate total number of chunks to process"""
        chunk_samples = int(self.chunk_duration * sampling_rate)
        return int(np.ceil(total_samples / chunk_samples))
    
    def get_chunk_boundaries(self, chunk_num: int, sampling_rate: float, 
                           total_samples: int) -> Tuple[int, int]:
        """Calculate chunk boundaries in samples"""
        chunk_samples = int(self.chunk_duration * sampling_rate)
        start_sample = chunk_num * chunk_samples
        end_sample = min((chunk_num + 1) * chunk_samples, total_samples)
        return start_sample, end_sample
    
    def extract_chunk_data(self, data: np.ndarray, chunk_num: int, 
                          sampling_rate: float) -> Tuple[np.ndarray, float, float]:
        """Extract chunk data and calculate time range"""
        start_sample, end_sample = self.get_chunk_boundaries(
            chunk_num, sampling_rate, data.shape[1]
        )
        
        # Extract chunk data
        chunk_data = data[:, start_sample:end_sample]
        
        # Calculate time range
        start_time = start_sample / sampling_rate
        end_time = end_sample / sampling_rate
        
        return chunk_data, start_time, end_time
    
    def process_preview(self, data: np.ndarray, sampling_rate: float,
                       preview_duration: int = 30) -> Dict[str, Any]:
        """Process first N seconds for immediate preview"""
        try:
            # Calculate sample indices for preview
            preview_samples = int(preview_duration * sampling_rate)
            preview_samples = min(preview_samples, data.shape[1])
            
            # Extract preview data
            preview_data = data[:, :preview_samples]
            
            return {
                "preview_duration": preview_duration,
                "preview_samples": preview_samples,
                "preview_data": preview_data,
                "sampling_rate": sampling_rate
            }
            
        except Exception as e:
            logger.error(f"Error processing preview: {e}")
            return {"error": str(e)}