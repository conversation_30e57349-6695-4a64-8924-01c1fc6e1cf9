"""
HFO detector service.
Wraps the core HFO detection algorithm with service functionality.
"""

import sys
import logging
from typing import Dict, Any, Optional, Callable, Tuple
from core.hfo_engine.pipeline import HFODetectionPipeline

# Ensure hfo_engine is in path
sys.path.append('./core/hfo_engine')

logger = logging.getLogger(__name__)


class HFODetectorService:
    """Service wrapper for HFO detection"""
    
    def __init__(self):
        """Initialize the HFO detector service"""
        self.logger = logging.getLogger(self.__class__.__name__)
        self.pipeline = HFODetectionPipeline()
        self._last_result = None
    
    def detect_hfos(
        self,
        eeg_data: Dict[str, Any],
        file_path: str,
        parameters: Dict[str, Any],
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """
        Run HFO detection on EEG data.
        
        Args:
            eeg_data: Prepared EEG data structure
            file_path: Path to the original EDF file
            parameters: Detection parameters
            progress_callback: Optional callback for progress updates
            
        Returns:
            Detection results
        """
        try:
            # Log detection start
            self.logger.info(f"Starting HFO detection for {file_path}")
            
            # Prepare parameters for the algorithm
            algo_params = self._prepare_algorithm_parameters(parameters)
            
            # Run detection through the pipeline
            result = self.pipeline.run(
                eeg_data=eeg_data,
                file_path=file_path,
                parameters=algo_params,
                gui_output=progress_callback
            )
            
            # Store result
            self._last_result = result
            
            # Log completion
            if result.get('success'):
                self.logger.info("HFO detection completed successfully")
            else:
                self.logger.warning("HFO detection completed with warnings")
            
            return result
            
        except Exception as e:
            self.logger.error(f"HFO detection failed: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _prepare_algorithm_parameters(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare parameters for the core algorithm.
        
        Args:
            params: User parameters
            
        Returns:
            Algorithm-compatible parameters
        """
        # Map parameter names if needed
        algo_params = {
            'analysis_start': params.get('timeSegment', {}).get('startTime', 0),
            'analysis_end': params.get('timeSegment', {}).get('endTime', -1),
            'montage': params.get('montage', {}).get('type', 'Bipolar montage'),
            'user_ref': params.get('montage', {}).get('referenceChannel'),
            'low_cutoff': params.get('frequency', {}).get('lowCutoff', 50),
            'high_cutoff': params.get('frequency', {}).get('highCutoff', 300),
            'amplitude_1': params.get('thresholds', {}).get('amplitude1', 2),
            'amplitude_2': params.get('thresholds', {}).get('amplitude2', 2),
            'peaks_1': params.get('thresholds', {}).get('peaks1', 6),
            'peaks_2': params.get('thresholds', {}).get('peaks2', 3),
            'duration': params.get('thresholds', {}).get('duration', 10),
            'temporal_sync': params.get('thresholds', {}).get('temporalSync', 10),
            'spatial_sync': params.get('thresholds', {}).get('spatialSync', 10)
        }
        
        return algo_params
    
    def get_last_result(self) -> Optional[Dict[str, Any]]:
        """Get the last detection result"""
        return self._last_result
    
    def validate_parameters(self, params: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """
        Validate detection parameters.
        
        Args:
            params: Parameters to validate
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        # Check frequency range
        freq = params.get('frequency', {})
        low = freq.get('lowCutoff', 50)
        high = freq.get('highCutoff', 300)
        
        if low >= high:
            return False, "Low cutoff must be less than high cutoff"
        
        if low < 1:
            return False, "Low cutoff must be at least 1 Hz"
        
        # Check thresholds
        thresholds = params.get('thresholds', {})
        
        for key, min_val in [
            ('amplitude1', 1), ('amplitude2', 1),
            ('peaks1', 2), ('peaks2', 2),
            ('duration', 5), ('temporalSync', 5), ('spatialSync', 5)
        ]:
            val = thresholds.get(key, 0)
            if val < min_val:
                return False, f"{key} must be at least {min_val}"
        
        return True, None