import sys
import numpy as np
import logging
from typing import Dict, Any, Optional, List, Tuple
from scipy.signal import butter, filtfilt, hilbert
import asyncio

logger = logging.getLogger(__name__)

# Add hfo_engine to path
sys.path.append('./core/hfo_engine')

from hfo_characteristics import calculate_HFO_characteristics
from hfo_proximity import is_hfo_nearby
from processing.filters import create_notch_filter


class ChunkedHFODetector:
    """Service for chunked HFO detection that processes data incrementally"""
    
    def __init__(self):
        self.hfo_results = {
            'final_start_ind': [],
            'final_end_ind': [],
            'final_chan': [],
            'final_dur': [],
            'final_amp': [],
            'final_freq': [],
            'final_pow': [],
            'final_powRatio': [],
            'rejected_start_ind': [],
            'rejected_end_ind': [],
            'rejecLONG_start_ind': [],
            'rejecLONG_end_ind': [],
            'success': True,
            'myEEG': None,
            'channel_labels': []
        }
        self.total_hfos = 0
        self.processed_samples = 0
        self.overlap_buffer = {}  # Store overlap data between chunks
        self.chunk_results = []  # Store results per chunk
        
    def initialize_detection(self, 
                           header: Dict[str, Any],
                           parameters: Any,
                           montage_type: str,
                           user_ref: str = "") -> bool:
        """Initialize detection parameters from header and analysis parameters"""
        try:
            # Store parameters
            self.parameters = parameters
            self.montage_type = montage_type
            self.user_ref = user_ref
            
            # Extract parameters
            self.thresh = parameters.thresholds.amplitude1
            self.thresh2 = parameters.thresholds.amplitude2
            self.min_HFO_len = parameters.thresholds.duration
            self.num_peaks = parameters.thresholds.peaks1
            self.num_peaks2 = parameters.thresholds.peaks2
            self.min_break = parameters.thresholds.temporal_sync
            self.con_delay = parameters.thresholds.spatial_sync
            
            self.locutoff = parameters.frequency.low_cutoff
            self.hicutoff = parameters.frequency.high_cutoff
            
            # Extract from header
            self.samp_freq = header['frequency'][0] if isinstance(header['frequency'], np.ndarray) else header['frequency']
            self.channel_labels = header['label']
            self.num_channels = len(self.channel_labels)
            
            # Calculate filter parameters
            self.win_len = 5  # Window length in ms
            self.win_len2 = round(self.win_len * self.samp_freq / 1000)
            
            # Design bandpass filter
            self.nyquist = self.samp_freq / 2
            self.b, self.a = butter(3, [self.locutoff / self.nyquist, self.hicutoff / self.nyquist], 'bandpass')
            
            # Initialize overlap buffer for each channel (2 seconds of data)
            self.overlap_samples = int(2 * self.samp_freq)
            
            logger.info(f"Initialized chunked HFO detector: {self.locutoff}-{self.hicutoff}Hz, "
                       f"sampling rate: {self.samp_freq}Hz")
            
            return True
            
        except Exception as e:
            logger.error(f"Error initializing chunked HFO detector: {e}")
            return False
            
    async def process_chunk(self, 
                          chunk_data: np.ndarray,
                          chunk_start_time: float,
                          chunk_end_time: float,
                          chunk_index: int) -> Dict[str, Any]:
        """
        Process a single chunk of EEG data for HFO detection
        
        Args:
            chunk_data: EEG data array (channels x samples)
            chunk_start_time: Start time of chunk in seconds
            chunk_end_time: End time of chunk in seconds
            chunk_index: Index of the current chunk
            
        Returns:
            Dictionary containing HFOs detected in this chunk
        """
        try:
            chunk_start_sample = int(chunk_start_time * self.samp_freq)
            
            # Apply montage if needed
            processed_data, montage_labels = self._apply_montage(chunk_data)
            
            # Store montage labels on first chunk
            if chunk_index == 0:
                self.hfo_results['channel_labels'] = montage_labels
                
            # Prepare data with overlap from previous chunk
            if chunk_index > 0 and self.overlap_buffer:
                # Concatenate overlap buffer with current chunk
                combined_data = []
                for ch_idx in range(processed_data.shape[0]):
                    if ch_idx in self.overlap_buffer:
                        combined = np.concatenate([self.overlap_buffer[ch_idx], processed_data[ch_idx]])
                    else:
                        combined = processed_data[ch_idx]
                    combined_data.append(combined)
                processed_data = np.array(combined_data)
            else:
                # First chunk, no overlap
                self.overlap_buffer = {}
                
            # Store overlap for next chunk (last 2 seconds)
            if processed_data.shape[1] > self.overlap_samples:
                for ch_idx in range(processed_data.shape[0]):
                    self.overlap_buffer[ch_idx] = processed_data[ch_idx, -self.overlap_samples:]
                    
            # Detect HFOs in this chunk
            chunk_hfos = await self._detect_hfos_in_chunk(
                processed_data, 
                chunk_start_sample,
                chunk_index
            )
            
            # Store results
            self.chunk_results.append({
                'chunk_index': chunk_index,
                'start_time': chunk_start_time,
                'end_time': chunk_end_time,
                'hfos': chunk_hfos
            })
            
            # Update total HFO count
            chunk_hfo_count = len(chunk_hfos)
            self.total_hfos += chunk_hfo_count
            
            logger.info(f"Chunk {chunk_index}: Detected {chunk_hfo_count} HFOs "
                       f"({chunk_start_time:.1f}s - {chunk_end_time:.1f}s)")
            
            return {
                'chunk_index': chunk_index,
                'hfo_count': chunk_hfo_count,
                'hfos': chunk_hfos,
                'start_time': chunk_start_time,
                'end_time': chunk_end_time
            }
            
        except Exception as e:
            logger.error(f"Error processing chunk {chunk_index}: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return {
                'chunk_index': chunk_index,
                'hfo_count': 0,
                'hfos': [],
                'error': str(e)
            }
            
    async def _detect_hfos_in_chunk(self, 
                                  data: np.ndarray,
                                  chunk_start_sample: int,
                                  chunk_index: int) -> List[Dict[str, Any]]:
        """Detect HFOs in a chunk of data"""
        chunk_hfos = []
        
        # Process each channel
        for ch_idx in range(data.shape[0]):
            channel_data = data[ch_idx]
            
            # Skip if channel has too many zeros (likely artifact)
            if np.sum(channel_data == 0) > len(channel_data) * 0.5:
                continue
                
            try:
                # Apply bandpass filter
                filtered_data = filtfilt(self.b, self.a, channel_data)
                
                # Calculate RMS envelope
                rms_signal = self._calculate_rms(filtered_data)
                
                # Calculate baseline statistics
                baseline_mean = np.mean(rms_signal)
                baseline_std = np.std(rms_signal)
                
                # Threshold for HFO detection
                threshold = baseline_mean + self.thresh * baseline_std
                
                # Find potential HFOs
                above_thresh = rms_signal > threshold
                
                # Find start and end points of potential HFOs
                diff = np.diff(np.concatenate(([0], above_thresh.astype(int), [0])))
                starts = np.where(diff == 1)[0]
                ends = np.where(diff == -1)[0]
                
                # Process each potential HFO
                for start_idx, end_idx in zip(starts, ends):
                    # Check minimum duration
                    duration_ms = (end_idx - start_idx) * 1000 / self.samp_freq
                    if duration_ms < self.min_HFO_len:
                        continue
                        
                    # Extract HFO segment
                    hfo_segment = filtered_data[start_idx:end_idx]
                    
                    # Count peaks
                    peaks = self._count_peaks(hfo_segment)
                    if peaks < self.num_peaks:
                        continue
                        
                    # Calculate HFO characteristics
                    amplitude = np.max(np.abs(hfo_segment))
                    
                    # Calculate instantaneous frequency using Hilbert transform
                    analytic_signal = hilbert(hfo_segment)
                    instantaneous_phase = np.unwrap(np.angle(analytic_signal))
                    instantaneous_frequency = np.diff(instantaneous_phase) / (2.0 * np.pi) * self.samp_freq
                    mean_frequency = np.mean(instantaneous_frequency)
                    
                    # Adjust sample indices to global position
                    global_start = chunk_start_sample + start_idx
                    global_end = chunk_start_sample + end_idx
                    
                    # Create HFO entry
                    hfo = {
                        'channel': self.hfo_results['channel_labels'][ch_idx] if self.hfo_results['channel_labels'] else ch_idx,
                        'start_sample': global_start,
                        'end_sample': global_end,
                        'start_time': global_start / self.samp_freq,
                        'end_time': global_end / self.samp_freq,
                        'duration_ms': duration_ms,
                        'amplitude': amplitude,
                        'frequency': mean_frequency,
                        'num_peaks': peaks,
                        'chunk_index': chunk_index
                    }
                    
                    chunk_hfos.append(hfo)
                    
            except Exception as e:
                logger.warning(f"Error processing channel {ch_idx} in chunk {chunk_index}: {e}")
                continue
                
        return chunk_hfos
        
    def _calculate_rms(self, signal: np.ndarray) -> np.ndarray:
        """Calculate RMS envelope of signal"""
        # Pad signal for window
        pad_len = self.win_len2 // 2
        padded = np.pad(signal, pad_len, mode='edge')
        
        # Calculate RMS using sliding window
        rms = np.zeros(len(signal))
        for i in range(len(signal)):
            window = padded[i:i + self.win_len2]
            rms[i] = np.sqrt(np.mean(window ** 2))
            
        return rms
        
    def _count_peaks(self, signal: np.ndarray) -> int:
        """Count peaks in signal"""
        # Simple peak counting using zero crossings of derivative
        if len(signal) < 3:
            return 0
            
        diff = np.diff(signal)
        sign_changes = np.diff(np.sign(diff))
        peaks = np.sum(sign_changes < 0)  # Negative sign change indicates peak
        
        return peaks
        
    def _apply_montage(self, data: np.ndarray) -> Tuple[np.ndarray, List[str]]:
        """Apply montage to data and return processed data with channel labels"""
        
        if self.montage_type == "Bipolar":
            # Implement bipolar montage
            processed_data = []
            labels = []
            
            for i in range(len(self.channel_labels) - 1):
                # Check if channels form a consecutive pair
                label1 = self.channel_labels[i]
                label2 = self.channel_labels[i + 1]
                
                # Simple bipolar: subtract adjacent channels
                diff_signal = data[i] - data[i + 1]
                processed_data.append(diff_signal)
                labels.append(f"{label1}-{label2}")
                
            return np.array(processed_data), labels
            
        elif self.montage_type == "Average":
            # Average reference montage
            avg_signal = np.mean(data, axis=0)
            processed_data = data - avg_signal
            return processed_data, self.channel_labels
            
        elif self.montage_type == "Referential":
            # Referential montage
            if self.user_ref and self.user_ref in self.channel_labels:
                ref_idx = self.channel_labels.index(self.user_ref)
                ref_signal = data[ref_idx]
                processed_data = data - ref_signal
                return processed_data, self.channel_labels
            else:
                # If reference not found, return original data
                return data, self.channel_labels
                
        else:
            # No montage, return original data
            return data, self.channel_labels
            
    def get_cumulative_results(self) -> Dict[str, Any]:
        """Get all HFO results detected so far"""
        # Compile all HFOs from chunks
        all_hfos = []
        for chunk_result in self.chunk_results:
            all_hfos.extend(chunk_result['hfos'])
            
        # Sort by start time
        all_hfos.sort(key=lambda x: x['start_time'])
        
        # Convert to legacy format for compatibility
        if all_hfos:
            final_start_ind = [[hfo['start_sample'] for hfo in all_hfos]]
            final_end_ind = [[hfo['end_sample'] for hfo in all_hfos]]
            final_chan = [[all_hfos[0]['channel']] * len(all_hfos)]  # Simplified for now
            final_dur = [[hfo['duration_ms'] for hfo in all_hfos]]
            final_amp = [[hfo['amplitude'] for hfo in all_hfos]]
            final_freq = [[hfo['frequency'] for hfo in all_hfos]]
        else:
            final_start_ind = [[]]
            final_end_ind = [[]]
            final_chan = [[]]
            final_dur = [[]]
            final_amp = [[]]
            final_freq = [[]]
            
        return {
            'success': True,
            'final_start_ind': final_start_ind,
            'final_end_ind': final_end_ind,
            'final_chan': final_chan,
            'final_dur': final_dur,
            'final_amp': final_amp,
            'final_freq': final_freq,
            'channel_labels': self.hfo_results['channel_labels'],
            'total_hfos': self.total_hfos,
            'chunk_results': self.chunk_results
        }
        
    def get_total_hfos(self) -> int:
        """Get total number of HFOs detected"""
        return self.total_hfos
