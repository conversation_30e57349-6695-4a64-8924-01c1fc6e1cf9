import sys
import numpy as np
import logging
from typing import Dict, List, Any, Optional
from scipy.signal import butter, filtfilt, hilbert, find_peaks

logger = logging.getLogger(__name__)

# Add hfo_engine to path
sys.path.append('./core/hfo_engine')

from hfo_analysis import run_hfo_algorithm


class ChunkedHFODetector:
    """Service for chunked HFO detection that processes data incrementally"""

    def __init__(self):
        self.hfo_results = {
            'final_start_ind': [],
            'final_end_ind': [],
            'final_chan': [],
            'final_dur': [],
            'final_amp': [],
            'final_freq': [],
            'final_pow': [],
            'final_powRatio': [],
            'rejected_start_ind': [],
            'rejected_end_ind': [],
            'rejecLONG_start_ind': [],
            'rejecLONG_end_ind': [],
            'success': True,
            'myEEG': None,
            'channel_labels': []
        }
        self.total_hfos = 0
        self.processed_samples = 0
        self.overlap_buffer = {}  # Store overlap data between chunks
        self.chunk_results = []  # Store results per chunk

        # Detection parameters (initialized during setup)
        self.samp_freq = None
        self.thresh = 2.0  # Amplitude threshold multiplier
        self.num_peaks = 6  # Minimum number of peaks
        self.min_HFO_len = 10  # Minimum HFO duration in ms
        self.overlap_samples = None  # Overlap buffer size in samples
        self.b = None  # Filter coefficients
        self.a = None

        # Performance optimizations
        self.first_chunk_processed = False
        self.filter_cache = {}  # Cache filtered data for reuse
        self.baseline_stats_cache = {}  # Cache baseline statistics

    def initialize_detection(self, header: Dict[str, Any], parameters: Any,
                           montage_type: str, user_ref: str = "") -> bool:
        """Initialize HFO detection parameters for chunked processing"""
        try:
            # Extract sampling rate
            self.samp_freq = header.get('srate', header.get('fs', 1000))

            # Set overlap buffer size (2 seconds)
            self.overlap_samples = int(2 * self.samp_freq)

            # Extract detection parameters
            self.thresh = parameters.thresholds.amplitude1
            self.num_peaks = parameters.thresholds.peaks1
            self.min_HFO_len = parameters.thresholds.duration

            # Design bandpass filter for HFO detection
            low_cutoff = parameters.frequency.low_cutoff
            high_cutoff = parameters.frequency.high_cutoff
            nyquist = self.samp_freq / 2

            # Ensure cutoff frequencies are valid
            low_norm = max(low_cutoff / nyquist, 0.01)
            high_norm = min(high_cutoff / nyquist, 0.99)

            # Create 6th-order Butterworth bandpass filter
            self.b, self.a = butter(6, [low_norm, high_norm], btype='band')

            logger.info(f"Initialized chunked HFO detector: {low_cutoff}-{high_cutoff}Hz, "
                       f"thresh={self.thresh}, peaks={self.num_peaks}")

            return True

        except Exception as e:
            logger.error(f"Error initializing chunked HFO detector: {e}")
            return False

    async def process_chunk(self, chunk_data: np.ndarray, chunk_start_time: float,
                          chunk_end_time: float, chunk_index: int) -> Dict[str, Any]:
        """
        Process a chunk of EEG data for HFO detection

        Args:
            chunk_data: EEG data for this chunk (channels x samples)
            chunk_start_time: Start time of chunk in seconds
            chunk_end_time: End time of chunk in seconds
            chunk_index: Index of this chunk

        Returns:
            Dictionary containing HFOs detected in this chunk
        """
        try:
            chunk_start_sample = int(chunk_start_time * self.samp_freq)

            # Apply montage if needed
            processed_data, montage_labels = self._apply_montage(chunk_data)

            # Store montage labels on first chunk
            if chunk_index == 0:
                self.hfo_results['channel_labels'] = montage_labels

            # Prepare data with overlap from previous chunk
            if chunk_index > 0 and self.overlap_buffer:
                # Concatenate overlap buffer with current chunk
                combined_data = []
                for ch_idx in range(processed_data.shape[0]):
                    if ch_idx in self.overlap_buffer:
                        combined = np.concatenate([self.overlap_buffer[ch_idx], processed_data[ch_idx]])
                    else:
                        combined = processed_data[ch_idx]
                    combined_data.append(combined)
                processed_data = np.array(combined_data)
            else:
                # First chunk, no overlap
                self.overlap_buffer = {}

            # Store overlap for next chunk (last 2 seconds)
            if processed_data.shape[1] > self.overlap_samples:
                for ch_idx in range(processed_data.shape[0]):
                    self.overlap_buffer[ch_idx] = processed_data[ch_idx, -self.overlap_samples:]

            # Detect HFOs in this chunk
            chunk_hfos = await self._detect_hfos_in_chunk(
                processed_data,
                chunk_start_sample,
                chunk_index
            )

            # Store results
            self.chunk_results.append({
                'chunk_index': chunk_index,
                'start_time': chunk_start_time,
                'end_time': chunk_end_time,
                'hfos': chunk_hfos
            })

            # Update total HFO count
            chunk_hfo_count = len(chunk_hfos)
            self.total_hfos += chunk_hfo_count

            # Mark first chunk as processed for performance tracking
            if chunk_index == 0:
                self.first_chunk_processed = True
                logger.info(f"First chunk processed in optimized mode: {chunk_hfo_count} HFOs detected")

            # Clean up old chunks periodically to manage memory
            if chunk_index > 0 and chunk_index % 10 == 0:
                self.cleanup_old_chunks(keep_last_n=5)

            logger.info(f"Chunk {chunk_index}: Detected {chunk_hfo_count} HFOs "
                       f"({chunk_start_time:.1f}s - {chunk_end_time:.1f}s)")

            return {
                'chunk_index': chunk_index,
                'hfo_count': chunk_hfo_count,
                'hfos': chunk_hfos,
                'start_time': chunk_start_time,
                'end_time': chunk_end_time
            }

        except Exception as e:
            logger.error(f"Error processing chunk {chunk_index}: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return {
                'chunk_index': chunk_index,
                'hfo_count': 0,
                'hfos': [],
                'error': str(e)
            }

    async def _detect_hfos_in_chunk(self,
                                  data: np.ndarray,
                                  chunk_start_sample: int,
                                  chunk_index: int) -> List[Dict[str, Any]]:
        """Detect HFOs in a chunk of data"""
        chunk_hfos = []

        # Process each channel
        for ch_idx in range(data.shape[0]):
            channel_data = data[ch_idx]

            # Skip if channel has too many zeros (likely artifact)
            if np.sum(channel_data == 0) > len(channel_data) * 0.5:
                continue

            try:
                # Apply bandpass filter (with caching for performance)
                cache_key = f"ch_{ch_idx}_chunk_{chunk_index}"
                if cache_key in self.filter_cache:
                    filtered_data = self.filter_cache[cache_key]
                else:
                    filtered_data = filtfilt(self.b, self.a, channel_data)
                    # Cache only for first few chunks to save memory
                    if chunk_index < 3:
                        self.filter_cache[cache_key] = filtered_data

                # Calculate RMS envelope (optimized for speed)
                rms_signal = self._calculate_rms_fast(filtered_data)

                # Calculate baseline statistics (with caching)
                baseline_key = f"ch_{ch_idx}"
                if baseline_key in self.baseline_stats_cache and chunk_index > 0:
                    # Use cached baseline for subsequent chunks
                    baseline_mean, baseline_std = self.baseline_stats_cache[baseline_key]
                else:
                    baseline_mean = np.mean(rms_signal)
                    baseline_std = np.std(rms_signal)
                    # Cache baseline stats from first chunk
                    if chunk_index == 0:
                        self.baseline_stats_cache[baseline_key] = (baseline_mean, baseline_std)

                # Threshold for HFO detection
                threshold = baseline_mean + self.thresh * baseline_std

                # Find potential HFOs
                above_thresh = rms_signal > threshold

                # Find start and end points of potential HFOs
                diff = np.diff(np.concatenate(([0], above_thresh.astype(int), [0])))
                starts = np.where(diff == 1)[0]
                ends = np.where(diff == -1)[0]

                # Process each potential HFO
                for start_idx, end_idx in zip(starts, ends):
                    # Check minimum duration
                    duration_ms = (end_idx - start_idx) * 1000 / self.samp_freq
                    if duration_ms < self.min_HFO_len:
                        continue

                    # Extract HFO segment
                    hfo_segment = filtered_data[start_idx:end_idx]

                    # Count peaks
                    peaks = self._count_peaks(hfo_segment)
                    if peaks < self.num_peaks:
                        continue

                    # Calculate HFO characteristics
                    amplitude = np.max(np.abs(hfo_segment))

                    # Calculate instantaneous frequency using Hilbert transform
                    analytic_signal = hilbert(hfo_segment)
                    instantaneous_phase = np.unwrap(np.angle(analytic_signal))
                    instantaneous_frequency = np.diff(instantaneous_phase) / (2.0 * np.pi) * self.samp_freq
                    mean_frequency = np.mean(instantaneous_frequency)

                    # Adjust sample indices to global position
                    global_start = chunk_start_sample + start_idx
                    global_end = chunk_start_sample + end_idx

                    # Create HFO entry
                    hfo = {
                        'channel': self.hfo_results['channel_labels'][ch_idx] if self.hfo_results['channel_labels'] else ch_idx,
                        'start_sample': global_start,
                        'end_sample': global_end,
                        'start_time': global_start / self.samp_freq,
                        'end_time': global_end / self.samp_freq,
                        'duration_ms': duration_ms,
                        'amplitude': amplitude,
                        'frequency': mean_frequency,
                        'num_peaks': peaks,
                        'chunk_index': chunk_index
                    }

                    chunk_hfos.append(hfo)

            except Exception as e:
                logger.warning(f"Error processing channel {ch_idx} in chunk {chunk_index}: {e}")
                continue

        return chunk_hfos

    def _apply_montage(self, data: np.ndarray) -> tuple:
        """Apply montage to data (simplified for now)"""
        # For now, return data as-is with channel labels
        num_channels = data.shape[0]
        labels = [f"CH{i+1}" for i in range(num_channels)]
        return data, labels

    def _calculate_rms(self, signal: np.ndarray, window_size: int = 100) -> np.ndarray:
        """Calculate RMS envelope of signal"""
        rms = np.zeros(len(signal))
        half_window = window_size // 2

        for i in range(len(signal)):
            start = max(0, i - half_window)
            end = min(len(signal), i + half_window)
            rms[i] = np.sqrt(np.mean(signal[start:end] ** 2))

        return rms

    def _calculate_rms_fast(self, signal: np.ndarray, window_size: int = 50) -> np.ndarray:
        """Fast RMS calculation using convolution (optimized for first chunk)"""
        # Use smaller window for faster processing
        if len(signal) < window_size:
            return np.abs(signal)  # Fallback for very short signals

        # Use convolution for faster RMS calculation
        squared_signal = signal ** 2
        kernel = np.ones(window_size) / window_size

        # Use 'same' mode to maintain signal length
        rms_squared = np.convolve(squared_signal, kernel, mode='same')
        rms = np.sqrt(np.maximum(rms_squared, 0))  # Ensure non-negative

        return rms

    def _count_peaks(self, signal: np.ndarray) -> int:
        """Count peaks in signal"""
        peaks, _ = find_peaks(np.abs(signal))
        return len(peaks)

    def get_total_hfos(self) -> int:
        """Get total number of HFOs detected so far"""
        return self.total_hfos

    def get_cumulative_results(self) -> Dict[str, Any]:
        """Get cumulative results from all processed chunks"""
        return {
            'total_hfos': self.total_hfos,
            'chunk_results': self.chunk_results,
            'success': True
        }

    def cleanup_old_chunks(self, keep_last_n: int = 5):
        """Clean up old chunk results to manage memory usage"""
        if len(self.chunk_results) > keep_last_n:
            # Keep only the last N chunks
            self.chunk_results = self.chunk_results[-keep_last_n:]
            logger.debug(f"Cleaned up old chunk results, keeping last {keep_last_n}")

    def get_memory_usage_estimate(self) -> Dict[str, float]:
        """Estimate current memory usage in MB"""
        import sys

        # Estimate overlap buffer size
        overlap_size = 0
        for ch_data in self.overlap_buffer.values():
            overlap_size += ch_data.nbytes if hasattr(ch_data, 'nbytes') else sys.getsizeof(ch_data)

        # Estimate chunk results size
        results_size = sys.getsizeof(self.chunk_results)

        return {
            'overlap_buffer_mb': overlap_size / (1024 * 1024),
            'chunk_results_mb': results_size / (1024 * 1024),
            'total_mb': (overlap_size + results_size) / (1024 * 1024)
        }