# Biormika HFO Detector - Complete Analysis Flow Documentation

## Table of Contents
1. [Project Overview](#project-overview)
2. [Architecture](#architecture)
3. [Complete User Journey](#complete-user-journey)
4. [Detailed Flow Analysis](#detailed-flow-analysis)
5. [API Endpoints Reference](#api-endpoints-reference)
6. [WebSocket Communication](#websocket-communication)
7. [Code Navigation Guide](#code-navigation-guide)

---

## Project Overview

The Biormika HFO Detector is a web-based application for detecting High-Frequency Oscillations (HFOs) in EEG data. HFOs are brief oscillatory events (80-500 Hz) that serve as biomarkers for epileptogenic tissue. The application processes EDF (European Data Format) files to automatically detect and characterize these events.

### Key Technologies
- **Frontend**: Next.js 15.4.5, React 19.1.0, TypeScript, Tailwind CSS, Ant Design
- **Backend**: FastAPI (Python), WebSocket support
- **Processing**: Original PyQt5 HFO detection algorithm preserved
- **Data Format**: EDF (European Data Format) files

---

## Architecture

### Frontend Structure
```
frontend/
├── src/
│   ├── app/
│   │   └── page.tsx                 # Main application page
│   ├── components/
│   │   ├── FileUploadCard.tsx       # File selection UI
│   │   ├── ParameterSettingsCard.tsx # Parameter configuration
│   │   ├── EEGViewer/               # Real-time visualization
│   │   ├── ParameterSections/       # Individual parameter sections
│   │   └── common/                   # Shared components
│   ├── hooks/
│   │   ├── useAppWorkflow.ts        # Main workflow management
│   │   ├── useFileAnalysis.ts       # File validation logic
│   │   └── useAnalysisOrchestration.ts # Analysis coordination
│   ├── contexts/
│   │   └── WebSocketContext.tsx     # WebSocket management
│   ├── services/
│   │   └── analysisValidationService.ts # Parameter validation
│   └── utils/
│       ├── api.ts                   # API communication
│       ├── validation.ts            # Frontend validation
│       └── transformers.ts          # Data format conversion
```

### Backend Structure
```
backend/
├── app.py                           # FastAPI application entry
├── api/
│   ├── routes/
│   │   ├── analysis.py             # Analysis endpoints
│   │   ├── parameters.py           # Parameter options
│   │   └── websocket.py            # WebSocket endpoint
│   ├── dependencies.py             # Shared dependencies
│   └── middleware.py               # CORS configuration
├── core/
│   ├── hfo_engine/                 # Original HFO detection algorithm
│   ├── validators/
│   │   ├── edf_validator.py        # EDF file validation
│   │   ├── parameter_validator.py  # Parameter validation
│   │   └── signal_validator.py     # Signal quality checks
│   └── exceptions/
│       └── validation_exceptions.py # Custom exceptions
├── models/
│   └── parameters.py               # Pydantic models
└── services/
    └── analysis_service.py         # EEGStreamProcessor
```

---

## Complete User Journey

### Step 1: File Selection
1. User enters EDF file path in FileUploadCard
2. Client-side validation checks:
   - Path not empty
   - .edf extension present
   - No invalid characters
3. User clicks "Validate" button
4. API call to `/api/analyze` for server-side validation
5. Backend validates file and extracts metadata
6. On success, "Continue to Parameter Settings" button appears

### Step 2: Parameter Configuration
1. ParameterSettingsCard loads with file metadata
2. Five parameter sections available:
   - Threshold Parameters (HFO detection sensitivity)
   - Montage Configuration (channel reference type)
   - Frequency Bands (filter settings)
   - Time Segment (analysis duration)
   - Channel Selection (which channels to analyze)
3. Real-time validation as parameters change
4. User clicks "Start Analysis" button

### Step 3: Analysis Execution
1. Frontend validates and prepares parameters
2. API call to `/api/analyze/start` for final validation
3. Backend performs comprehensive validation
4. On success, UI transitions to EEGViewer
5. WebSocket connection established to `ws://localhost:8000/ws`
6. Backend processes EDF in 10-second chunks
7. Real-time HFO detection results streamed to frontend
8. Progress updates and visualization in EEGViewer

---

## Detailed Flow Analysis

### 1. FILE UPLOAD FLOW

#### Frontend: FileUploadCard Component
**Location**: `frontend/src/components/FileUploadCard.tsx`

**User Actions**:
1. User types file path into input field
2. Clicks "Validate" button or presses Enter

**Code Execution**:
```typescript
// Line 49-62: handleValidate function
const handleValidate = useCallback(() => {
  const validationError = validatePath(filepath);  // Client-side validation
  
  if (validationError) {
    setLocalError(validationError);
    setHasValidated(true);
    return;
  }
  
  setLocalError("");
  setHasValidated(true);
  onFileSelect(filepath);  // Triggers API call
}, [filepath, onFileSelect]);
```

#### Frontend: useAppWorkflow Hook
**Location**: `frontend/src/hooks/useAppWorkflow.ts`

**Code Execution**:
```typescript
// Lines 13-27: handleFileSelect
const handleFileSelect = useCallback(async (selectedFilepath: string) => {
  setFilepath(selectedFilepath);
  clearError();

  if (!selectedFilepath) {
    return;
  }

  const success = await loadFileInfo(selectedFilepath);  // API call
  
  if (success) {
    setAppState("settings-configuration");  // Transition to next step
  }
}, [loadFileInfo, clearError]);
```

#### Frontend: API Call
**Location**: `frontend/src/utils/api.ts`

**Code Execution**:
```typescript
// Lines 17-82: getFileInfo function
export async function getFileInfo(filepath: string): Promise<FileInfo> {
  const response = await fetch(`${API_BASE_URL}/api/analyze`, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ filepath }),
  });
  // ... error handling and response processing
}
```

#### Backend: /api/analyze Endpoint
**Location**: `backend/api/routes/analysis.py`

**Line-by-line execution (Lines 19-72)**:

```python
@router.post("/analyze")
async def analyze(request: FileAnalyzeRequest):
    """Start analysis of a local EDF file with comprehensive validation"""
    
    # Line 24-31: File validation
    is_valid, errors = deps.edf_validator.validate_file(request.filepath)
    # Checks: file exists, readable, .edf extension, min size 256 bytes
    
    # Line 33-48: Extract and validate EDF header
    header = extract_edf_metadata(request.filepath)
    # Reads EDF header: channels, sampling rate, duration, dates
    
    # Line 35-42: Header validation
    is_valid, errors = deps.edf_validator.validate_header(header)
    # Checks: required fields, channel count > 0, sampling rate >= 200 Hz
    
    # Line 50: Store filepath for WebSocket
    deps.current_filepath = request.filepath
    
    # Line 52: Extract sampling rate
    sampling_rate = SamplingRateUtils.extract_sampling_rate(header['frequency'])
    
    # Lines 54-66: Return file information
    return {
        "status": "ready",
        "filepath": request.filepath,
        "file_info": {
            "channels": header['label'],
            "sampling_rate": sampling_rate,
            "duration_seconds": header['records'] * header['duration'],
            "start_date": header['startdate'],
            "start_time": header['starttime']
        }
    }
```

### 2. PARAMETER CONFIGURATION FLOW

#### Frontend: ParameterSettingsCard
**Location**: `frontend/src/components/ParameterSettingsCard.tsx`

**Component Structure**:
- Displays file information
- Five collapsible parameter sections
- Real-time validation feedback
- Action buttons (Back, Reset, Start Analysis)

**Key Functions**:

```typescript
// Lines 66-72: Parameter validation on change
useEffect(() => {
  const errors = validateAllParameters(parameters, fileInfo);
  setValidationErrors(errors);
  const isValid = areParametersValid(errors);
  onValidationChange(isValid, errors);
  onParametersChange(parameters);
}, [parameters, fileInfo, onValidationChange, onParametersChange]);

// Lines 74-93: Update parameters with validation
const updateParameters = (updates: Partial<AnalysisParameters>) => {
  // Special handling for bipolar montage
  if (updates.montage?.type === 'bipolar') {
    // Check if enough channels selected
    if (selectedChannels < 2) {
      message.warning('⚠️ Bipolar montage requires at least 2 channels...');
    }
  }
  setParameters((prev) => ({ ...prev, ...updates }));
};
```

### 3. START ANALYSIS BUTTON CLICK - DETAILED FLOW

#### Frontend: Button Click Event
**Location**: `frontend/src/components/ParameterSettingsCard.tsx:245`

```typescript
<Button
  type="primary"
  onClick={onStartAnalysis}  // Triggers the flow
  // ...
>
```

#### Frontend: useAnalysisOrchestration Hook
**Location**: `frontend/src/hooks/useAnalysisOrchestration.ts`

**Complete handleStartAnalysis Function (Lines 29-97)**:

```typescript
const handleStartAnalysis = useCallback(async () => {
  // Step 1: Validate prerequisites
  if (!filepath || !fileInfo) {
    message.error("Please select a valid EDF file before starting analysis");
    return;
  }

  // Step 2: Prepare and validate parameters
  const { finalParameters, validationResult } = prepareAnalysisParameters(parameters, fileInfo);

  // Step 3: Handle validation failures
  if (!validationResult.isValid) {
    const errorCount = getErrorCount(validationResult.errors);
    
    // Show warning and apply defaults
    message.warning({
      content: `Found ${errorCount} validation issues. Using default values where possible.`,
      duration: 4,
    });

    // Update parameters with defaults
    onParametersUpdate(finalParameters);

    // Re-validate with defaults
    const { validationResult: newValidationResult } = prepareAnalysisParameters(finalParameters, fileInfo);

    // If still invalid, stop
    if (!newValidationResult.isValid) {
      message.error("Cannot start analysis: " + newValidationResult.errors.join(", "));
      return;
    }
  }

  // Step 4: Make API call
  try {
    setIsStarting(true);
    await startAnalysis(filepath, finalParameters);  // POST to /api/analyze/start
    
    message.info("Analysis started successfully. Switching to real-time view...");
    
    onSuccess();  // Transition to analysis view
  } catch (err) {
    message.error(`Analysis failed: ${err.message}`);
  } finally {
    setIsStarting(false);
  }
}, [filepath, parameters, fileInfo, onSuccess, onParametersUpdate]);
```

#### Backend: /api/analyze/start Endpoint - COMPLETE LINE-BY-LINE
**Location**: `backend/api/routes/analysis.py:75-189`

```python
@router.post("/analyze/start")
async def start_analysis(request: StartAnalysisRequest):
    """Start analysis with full parameter configuration"""
    
    try:
        # Lines 80-86: Validate file exists and is readable
        is_valid, errors = deps.edf_validator.validate_file(request.filepath)
        if not is_valid:
            raise ValidationError(
                message="EDF file validation failed",
                errors=errors,  # e.g., ["File not found", "Not an EDF file"]
                field="filepath"
            )
        
        # Lines 88-89: Extract EDF metadata
        from browse_files import extract_edf_metadata
        header = extract_edf_metadata(request.filepath)
        # header contains: label, frequency, records, duration, startdate, starttime
        
        # Lines 91-96: Validate EDF header structure
        is_valid, errors = deps.edf_validator.validate_header(header)
        if not is_valid:
            raise ValidationError(
                message="EDF header validation failed",
                errors=errors  # e.g., ["Sampling rate too low", "No channels found"]
            )
        
        # Line 98: Calculate sampling rate from frequency field
        sampling_rate = SamplingRateUtils.extract_sampling_rate(header['frequency'])
        # Handles single or multiple sampling rates, returns minimum
        
        # Lines 100-109: Initialize parameters with defaults if not provided
        if request.parameters:
            params = request.parameters
        else:
            params = {
                "thresholds": {},
                "montage": {"type": "bipolar"},
                "frequency": {"low_cutoff": 50, "high_cutoff": 300},
                "time_segment": {"mode": "entire_file"},
                "channel_selection": {"selected_leads": [], "contact_specifications": {}}
            }
        
        # Lines 111-120: Validate frequency parameters
        if params.get("frequency"):
            high_cutoff = params["frequency"].get("high_cutoff", 300)
            max_freq = sampling_rate / 3  # Nyquist-related constraint
            
            if high_cutoff > max_freq:
                raise ValidationError(
                    message=f"High cutoff frequency ({high_cutoff}Hz) exceeds maximum usable frequency ({max_freq:.1f}Hz) for sampling rate {sampling_rate}Hz",
                    errors=[f"Maximum frequency should be sampling_rate/3 = {max_freq:.1f}Hz"],
                    field="frequency.high_cutoff"
                )
        
        # Lines 122-147: Validate time segment parameters
        if params.get("time_segment"):
            time_seg = params["time_segment"]
            file_duration = header['records'] * header['duration']  # Total seconds
            
            # Validate start_end_times mode
            if time_seg.get("mode") == "start_end_times":
                if not all([time_seg.get("start_date"), time_seg.get("start_time"),
                           time_seg.get("end_date"), time_seg.get("end_time")]):
                    raise ValidationError(
                        message="Start/end times mode requires all date/time fields",
                        errors=["Missing required date/time fields"],
                        field="time_segment"
                    )
            
            # Validate start_time_duration mode
            elif time_seg.get("mode") == "start_time_duration":
                if not all([time_seg.get("start_date"), time_seg.get("start_time"),
                           time_seg.get("duration_seconds")]):
                    raise ValidationError(
                        message="Start time/duration mode requires start date/time and duration",
                        errors=["Missing required fields"],
                        field="time_segment"
                    )
                # Check duration doesn't exceed file
                if time_seg.get("duration_seconds", 0) > file_duration:
                    raise ValidationError(
                        message=f"Duration ({time_seg['duration_seconds']}s) exceeds file duration ({file_duration}s)",
                        errors=["Duration too long"],
                        field="time_segment.duration_seconds"
                    )
        
        # Lines 149-163: Validate montage configuration
        if params.get("montage"):
            montage = params["montage"]
            
            # Referential montage needs reference channel
            if montage.get("type") == "referential" and not montage.get("reference_channel"):
                raise ValidationError(
                    message="Referential montage requires a reference channel",
                    errors=["Missing reference channel"],
                    field="montage.reference_channel"
                )
            
            # Bipolar montage needs at least 2 channels
            if montage.get("type") == "bipolar" and len(header['label']) < 2:
                raise ValidationError(
                    message="Bipolar montage requires at least 2 channels",
                    errors=[f"Only {len(header['label'])} channel(s) available"],
                    field="montage.type"
                )
        
        # Line 165: Store filepath for WebSocket access
        deps.current_filepath = request.filepath
        
        # Line 167: Extract channel groups for intelligent selection
        channel_groups = _extract_channel_groups(header['label'])
        # Groups like: {'FP': [1, 2], 'C': [3, 4], 'O': [1, 2]}
        
        # Lines 169-183: Return comprehensive success response
        return {
            "status": "ready",
            "message": "Analysis parameters validated. Ready to start streaming.",
            "filepath": request.filepath,
            "file_info": {
                "channels": header['label'],  # All channel names
                "channel_groups": channel_groups,  # Grouped by lead type
                "sampling_rate": sampling_rate,  # Hz
                "max_frequency": sampling_rate / 3,  # Maximum analyzable frequency
                "duration_seconds": header['records'] * header['duration'],
                "start_date": header['startdate'],
                "start_time": header['starttime']
            },
            "validated_parameters": params  # Final parameters to be used
        }
        
    except ValidationError:
        raise  # Re-raise with proper HTTP error formatting
    except Exception as e:
        logger.error(f"Error in start_analysis: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
```

### 4. WEBSOCKET CONNECTION AND STREAMING

#### Frontend: WebSocket Provider
**Location**: `frontend/src/contexts/WebSocketContext.tsx`

**WebSocket Initialization (Lines 30-87)**:

```typescript
useEffect(() => {
  // Connect to WebSocket
  const ws = new WebSocket('ws://localhost:8000/ws');
  
  ws.onopen = () => {
    setIsConnected(true);
  };

  ws.onmessage = (event) => {
    const message = JSON.parse(event.data);
    
    switch (message.type) {
      case 'status':
        // Processing status updates
        break;
      
      case 'preview':
        // Initial data preview
        break;
      
      case 'chunk':
        // HFO detection results for 10-second chunk
        const transformedChunk = fromBackendFormat(message.data);
        setChunkResults(prev => [...prev, transformedChunk]);
        setProgress(transformedChunk.progress);
        break;
      
      case 'complete':
        // Analysis complete
        setProgress(100);
        break;
      
      case 'error':
        setError(message.message);
        break;
    }
  };
}, []);
```

#### Backend: WebSocket Endpoint
**Location**: `backend/api/routes/websocket.py:15-134`

**WebSocket Processing Flow**:

```python
@router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await websocket.accept()
    
    try:
        # Validate file was selected
        if not deps.current_filepath:
            await websocket.send_json({
                "type": "error",
                "message": "No file selected. Please call /api/analyze first."
            })
            return
        
        # Send status
        await websocket.send_json({
            "type": "status",
            "message": "Starting analysis..."
        })
        
        # Initialize parameters
        parameters = AnalysisParameters(**default_params)
        
        # Create processor
        processor = await deps.analysis_service.create_processor(deps.current_filepath, parameters)
        
        # Send preview
        preview_result = await processor.process_preview()
        await websocket.send_json({
            "type": "preview",
            "data": preview_result
        })
        
        # Process chunks
        total_chunks = processor.get_total_chunks()
        
        for chunk_num in range(total_chunks):
            # Process 10-second chunk
            chunk_result = await processor.process_chunk(chunk_num)
            
            # Calculate progress
            progress = ((chunk_num + 1) / total_chunks) * 100
            
            # Send chunk results
            await websocket.send_json({
                "type": "chunk",
                "data": {
                    "chunk_number": chunk_num,
                    "total_chunks": total_chunks,
                    "time_range": chunk_result["time_range"],
                    "hfo_events": chunk_result.get("hfo_events", []),  # Detected HFOs
                    "channel_data": chunk_result.get("channel_data", {}),  # EEG data
                    "progress": progress
                }
            })
            
            await asyncio.sleep(0.01)  # Yield control
        
        # Send completion
        await websocket.send_json({
            "type": "complete",
            "data": {
                "total_hfos": processor.get_total_hfos(),
                "summary": processor.get_summary()
            }
        })
        
    except WebSocketDisconnect:
        deps.active_websocket = None
    except Exception as e:
        await websocket.send_json({
            "type": "error",
            "message": str(e)
        })
```

---

## API Endpoints Reference

### POST /api/analyze
**Purpose**: Validate EDF file and extract metadata

**Request**:
```json
{
  "filepath": "/path/to/file.edf"
}
```

**Response**:
```json
{
  "status": "ready",
  "message": "File validated successfully...",
  "filepath": "/path/to/file.edf",
  "file_info": {
    "channels": ["FP1", "FP2", "C3", "C4"],
    "sampling_rate": 256,
    "duration_seconds": 3600,
    "start_date": "01.01.23",
    "start_time": "10:30:00"
  }
}
```

### POST /api/analyze/start
**Purpose**: Validate parameters and prepare for analysis

**Request**:
```json
{
  "filepath": "/path/to/file.edf",
  "parameters": {
    "thresholds": {
      "amplitude1": 2,
      "amplitude2": 2,
      "peaks1": 6,
      "peaks2": 3,
      "duration": 10,
      "temporal_sync": 10,
      "spatial_sync": 10
    },
    "montage": {
      "type": "bipolar",
      "reference_channel": null
    },
    "frequency": {
      "low_cutoff": 50,
      "high_cutoff": 300
    },
    "time_segment": {
      "mode": "entire_file"
    },
    "channel_selection": {
      "selected_leads": ["FP1", "FP2"],
      "contact_specifications": {}
    }
  }
}
```

**Response**:
```json
{
  "status": "ready",
  "message": "Analysis parameters validated. Ready to start streaming.",
  "file_info": {
    "channels": ["FP1", "FP2", "C3", "C4"],
    "channel_groups": {"FP": [1, 2], "C": [3, 4]},
    "sampling_rate": 256,
    "max_frequency": 85.33,
    "duration_seconds": 3600
  },
  "validated_parameters": {...}
}
```

---

## WebSocket Communication

### Connection
```javascript
ws://localhost:8000/ws
```

### Message Types

#### Status Message
```json
{
  "type": "status",
  "message": "Starting analysis..."
}
```

#### Preview Message
```json
{
  "type": "preview",
  "data": {
    "channels": ["FP1", "FP2"],
    "sample_data": [...]
  }
}
```

#### Chunk Message
```json
{
  "type": "chunk",
  "data": {
    "chunk_number": 0,
    "total_chunks": 360,
    "time_range": [0, 10],
    "hfo_events": [
      {
        "channel": "FP1",
        "start_time": 2.5,
        "end_time": 2.51,
        "amplitude": 45.2,
        "frequency": 250
      }
    ],
    "channel_data": {
      "FP1": [/* 2560 samples */],
      "FP2": [/* 2560 samples */]
    },
    "progress": 0.28
  }
}
```

#### Complete Message
```json
{
  "type": "complete",
  "data": {
    "total_hfos": 157,
    "summary": {
      "channels_analyzed": 4,
      "duration_analyzed": 3600,
      "hfos_per_channel": {
        "FP1": 45,
        "FP2": 38,
        "C3": 42,
        "C4": 32
      }
    }
  }
}
```

#### Error Message
```json
{
  "type": "error",
  "message": "Description of what went wrong",
  "traceback": "Full Python traceback (if available)"
}
```

---

## Code Navigation Guide

### Key Files to Understand the Flow

1. **Entry Points**:
   - Frontend: `frontend/src/app/page.tsx` - Main application component
   - Backend: `backend/app.py` - FastAPI application

2. **File Upload Flow**:
   - `frontend/src/components/FileUploadCard.tsx` - UI component
   - `frontend/src/hooks/useFileAnalysis.ts` - File validation logic
   - `backend/api/routes/analysis.py` - `/api/analyze` endpoint

3. **Parameter Configuration**:
   - `frontend/src/components/ParameterSettingsCard.tsx` - Main settings UI
   - `frontend/src/components/ParameterSections/*` - Individual parameter sections
   - `frontend/src/utils/validation.ts` - Frontend validation rules

4. **Analysis Start**:
   - `frontend/src/hooks/useAnalysisOrchestration.ts` - Orchestrates analysis start
   - `backend/api/routes/analysis.py` - `/api/analyze/start` endpoint
   - `backend/core/validators/*` - Backend validation logic

5. **Real-time Processing**:
   - `frontend/src/contexts/WebSocketContext.tsx` - WebSocket client
   - `backend/api/routes/websocket.py` - WebSocket server
   - `backend/services/analysis_service.py` - EEG processing logic

### Understanding Parameter Validation

#### Frontend Validation (`frontend/src/utils/validation.ts`)
- Validates parameter ranges
- Checks required fields
- Ensures logical consistency (e.g., low_cutoff < high_cutoff)

#### Backend Validation (`backend/core/validators/parameter_validator.py`)
- Validates against file capabilities
- Checks sampling rate constraints
- Ensures montage compatibility with available channels

### Understanding HFO Detection

The core HFO detection algorithm is in `backend/core/hfo_engine/`. Key parameters:

1. **Amplitude Thresholds**: 
   - amplitude1: HFO amplitude ≥ energy signal (× std)
   - amplitude2: HFO amplitude ≥ baseline (× std)

2. **Peak Counts**:
   - peaks1: Number of peaks ≥ amplitude1
   - peaks2: Number of peaks ≥ amplitude2

3. **Duration**: Minimum HFO length in milliseconds

4. **Synchronization**:
   - temporal_sync: Inter-HFO interval within channel
   - spatial_sync: Inter-HFO interval across channels

### Error Handling Strategy

1. **Client-side Validation**: Immediate feedback for basic errors
2. **API Validation**: Comprehensive file and parameter checks
3. **WebSocket Errors**: Real-time error streaming during processing
4. **User Feedback**: Toast messages and error alerts at each step

### State Management

The application uses three main states:
1. `file-selection`: Initial file upload
2. `settings-configuration`: Parameter setup
3. `analysis`: Real-time HFO detection

State transitions are managed by `useAppWorkflow` hook.

---

## Common Issues and Solutions

### File Not Found
- Ensure absolute path is provided
- Check file exists and has read permissions
- Verify .edf extension

### Sampling Rate Too Low
- Minimum 200 Hz required for HFO detection
- Check EDF file header for actual sampling rate

### Bipolar Montage Error
- Requires at least 2 channels
- Ensure enough channels are selected or available

### Frequency Validation Error
- High cutoff must be ≤ sampling_rate/3
- Adjust frequency bands based on file's sampling rate

### WebSocket Connection Failed
- Ensure backend is running on port 8000
- Check CORS settings if frontend on different port
- Verify no firewall blocking WebSocket connections

---

## Performance Considerations

1. **Chunked Processing**: 10-second chunks balance memory usage and responsiveness
2. **Streaming Results**: WebSocket provides real-time feedback without blocking
3. **Validation First**: Comprehensive validation prevents expensive failed processing
4. **Memory Management**: EDF files are not loaded entirely into memory

---

## Security Considerations

1. **File Path Validation**: Prevents directory traversal attacks
2. **Parameter Sanitization**: All inputs validated before processing
3. **CORS Configuration**: Restricted to specific origins
4. **Error Message Sanitization**: Sensitive paths not exposed in errors

---

This documentation provides a complete understanding of the Biormika HFO Detector application flow, from file selection through real-time analysis. Use this as a reference for understanding the codebase, debugging issues, or extending functionality.