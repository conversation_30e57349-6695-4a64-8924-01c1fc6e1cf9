"use client";

import React, { createContext, useContext, useEffect, useState, useRef } from "react";
import { ChunkResult, HFOEvent } from "@/types/eeg";
import { fromBackendFormat } from "@/utils/transformers";

interface ChunkedWebSocketContextType {
  isConnected: boolean;
  progress: number;
  chunkResults: ChunkResult[];
  hfoEvents: HFOEvent[];
  error: string | null;
  isInitialized: boolean;
  totalChunks: number;
  chunkDuration: number;
  useChunkedMode: boolean;
  setUseChunkedMode: (value: boolean) => void;
}

const ChunkedWebSocketContext = createContext<ChunkedWebSocketContextType>({
  isConnected: false,
  progress: 0,
  chunkResults: [],
  hfoEvents: [],
  error: null,
  isInitialized: false,
  totalChunks: 0,
  chunkDuration: 10,
  useChunkedMode: true,
  setUseChunkedMode: () => {},
});

export const useChunkedWebSocket = () => useContext(ChunkedWebSocketContext);

export const ChunkedWebSocketProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isConnected, setIsConnected] = useState(false);
  const [progress, setProgress] = useState(0);
  const [chunkResults, setChunkResults] = useState<ChunkResult[]>([]);
  const [hfoEvents, setHfoEvents] = useState<HFOEvent[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [totalChunks, setTotalChunks] = useState(0);
  const [chunkDuration, setChunkDuration] = useState(10);
  const [useChunkedMode, setUseChunkedMode] = useState(true);
  const wsRef = useRef<WebSocket | null>(null);

  useEffect(() => {
    // Use chunked or regular WebSocket based on mode
    const wsUrl = useChunkedMode ? "ws://localhost:8000/ws/chunked" : "ws://localhost:8000/ws";

    const ws = new WebSocket(wsUrl);
    wsRef.current = ws;

    ws.onopen = () => {
      console.log(`WebSocket connected (${useChunkedMode ? "chunked" : "regular"} mode)`);
      setIsConnected(true);
      setError(null);
      // Reset state
      setChunkResults([]);
      setHfoEvents([]);
      setProgress(0);
      setIsInitialized(false);
    };

    ws.onmessage = (event) => {
      const message = JSON.parse(event.data);
      console.log("WebSocket message:", message.type, message);

      switch (message.type) {
        case "status":
          console.log("Status:", message.message);
          break;

        case "initialization_complete":
          // New message type for chunked mode
          setIsInitialized(true);
          setTotalChunks(message.data.total_chunks);
          setChunkDuration(message.data.chunk_duration);
          console.log("Initialization complete:", message.data);
          break;

        case "preview":
          console.log("Preview data received:", message.data);
          break;

        case "hfo_batch":
          // Progressive HFO results
          if (message.data.hfos && Array.isArray(message.data.hfos)) {
            setHfoEvents((prev) => {
              const newHFOs = message.data.hfos.map((hfo: any) => ({
                type: hfo.type || "HFO",
                channel: hfo.channel,
                startTime: hfo.start_time,
                endTime: hfo.end_time,
                amplitude: hfo.amplitude,
                frequency: hfo.frequency,
                durationMs: hfo.duration_ms,
                sampleStart: hfo.sample_start,
                sampleEnd: hfo.sample_end,
              }));
              console.log(`Received ${newHFOs.length} new HFOs from chunk ${message.data.chunk_number}`);
              return [...prev, ...newHFOs];
            });
          }
          break;

        case "chunk":
          const transformedChunk = fromBackendFormat(message.data) as unknown as ChunkResult;

          // Add chunk to results
          setChunkResults((prev) => [...prev, transformedChunk]);
          setProgress(transformedChunk.progress);

          // Log chunk processing info
          if (message.data.hfo_count > 0) {
            console.log(`Chunk ${message.data.chunk_number}: ${message.data.hfo_count} HFOs detected`);
          }
          break;

        case "complete":
          console.log("Analysis complete:", message.data);
          setProgress(100);
          break;

        case "validation_error":
        case "error":
          setError(message.message);
          console.error("WebSocket error:", message);
          break;

        case "validation_warning":
          console.warn("Validation warnings:", message.warnings);
          break;
      }
    };

    ws.onerror = (error) => {
      console.error("WebSocket error:", error);
      setError("Connection error");
      setIsConnected(false);
    };

    ws.onclose = () => {
      console.log("WebSocket disconnected");
      setIsConnected(false);
    };

    return () => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.close();
      }
    };
  }, [useChunkedMode]); // Reconnect when mode changes

  return (
    <ChunkedWebSocketContext.Provider
      value={{
        isConnected,
        progress,
        chunkResults,
        hfoEvents,
        error,
        isInitialized,
        totalChunks,
        chunkDuration,
        useChunkedMode,
        setUseChunkedMode,
      }}
    >
      {children}
    </ChunkedWebSocketContext.Provider>
  );
};
