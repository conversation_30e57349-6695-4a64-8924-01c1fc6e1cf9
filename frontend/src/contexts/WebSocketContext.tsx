"use client";

import React, { createContext, useContext, useEffect, useState, useRef } from "react";
import { ChunkResult } from "@/types/eeg";
import { fromBackendFormat } from "@/utils/transformers";

interface WebSocketContextType {
  isConnected: boolean;
  progress: number;
  chunkResults: ChunkResult[];
  error: string | null;
}

const WebSocketContext = createContext<WebSocketContextType>({
  isConnected: false,
  progress: 0,
  chunkResults: [],
  error: null,
});

export const useWebSocket = () => useContext(WebSocketContext);

export const WebSocketProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isConnected, setIsConnected] = useState(false);
  const [progress, setProgress] = useState(0);
  const [chunkResults, setChunkResults] = useState<ChunkResult[]>([]);
  const [error, setError] = useState<string | null>(null);
  const wsRef = useRef<WebSocket | null>(null);

  useEffect(() => {
    const ws = new WebSocket("ws://localhost:8000/ws");
    wsRef.current = ws;

    ws.onopen = () => {
      console.log("WebSocket connected");
      setIsConnected(true);
      setError(null);
    };

    ws.onmessage = (event) => {
      const message = JSON.parse(event.data);
      console.log("WebSocket message:", message);

      switch (message.type) {
        case "status":
          console.log("Status:", message.message);
          break;

        case "preview":
          console.log("Preview data received:", message.data);
          break;

        case "chunk":
          const transformedChunk = fromBackendFormat(message.data) as unknown as ChunkResult;
          setChunkResults((prev) => [...prev, transformedChunk]);
          setProgress(transformedChunk.progress);
          break;

        case "complete":
          console.log("Analysis complete:", message.data);
          setProgress(100);
          break;

        case "error":
          setError(message.message);
          console.error("WebSocket error:", message);
          break;
      }
    };

    ws.onerror = (error) => {
      console.error("WebSocket error:", error);
      setError("Connection error");
      setIsConnected(false);
    };

    ws.onclose = () => {
      console.log("WebSocket disconnected");
      setIsConnected(false);
    };

    return () => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.close();
      }
    };
  }, []);

  return <WebSocketContext.Provider value={{ isConnected, progress, chunkResults, error }}>{children}</WebSocketContext.Provider>;
};
