import { ReactElement } from "react";
import FileUploadCard from "@/components/FileUploadCard";
import ParameterSettingsCard from "@/components/ParameterSettingsCard";
import EEGViewer from "@/components/EEGViewer";
import { WebSocketProvider } from "@/contexts/WebSocketContext";
import { AppState } from "@/types/app";
import { FileInfo, AnalysisParameters } from "@/types/eeg";

interface StepComponentProps {
  fileInfo: FileInfo | null;
  parameters: AnalysisParameters;
  error: string;
  isLoading: boolean;
  onFileSelect: (filepath: string) => Promise<void>;
  onParametersChange: (params: AnalysisParameters) => void;
  onValidationChange: (valid: boolean) => void;
  onStartAnalysis: () => Promise<void>;
  onBack: () => void;
}

export const renderStep = (
  appState: AppState,
  props: StepComponentProps
): ReactElement | null => {
  switch (appState) {
    case "file-selection":
      return (
        <FileUploadCard
          onFileSelect={props.onFileSelect}
          error={props.error}
          isLoading={props.isLoading}
        />
      );

    case "settings-configuration":
      return props.fileInfo ? (
        <ParameterSettingsCard
          fileInfo={props.fileInfo}
          onParametersChange={props.onParametersChange}
          onValidationChange={props.onValidationChange}
          onStartAnalysis={props.onStartAnalysis}
          onBack={props.onBack}
          initialParameters={props.parameters}
        />
      ) : null;

    case "analysis":
      return (
        <WebSocketProvider>
          <EEGViewer />
        </WebSocketProvider>
      );

    default:
      return null;
  }
};