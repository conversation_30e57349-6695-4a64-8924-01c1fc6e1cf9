import { Layout } from "antd";
import { ReactNode } from "react";
import { AppHeader } from "./AppHeader";

const { Content } = Layout;

interface AppLayoutProps {
  children: ReactNode;
}

export const AppLayout = ({ children }: AppLayoutProps) => {
  return (
    <Layout className="h-screen w-screen flex flex-col">
      <AppHeader />
      <Content className="flex-1 flex flex-col">
        <div className="flex-1 overflow-auto flex justify-center">
          <div className="w-full mx-auto px-4 py-8">
            {children}
          </div>
        </div>
      </Content>
    </Layout>
  );
};