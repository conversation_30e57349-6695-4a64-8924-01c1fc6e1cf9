import { LineChartOutlined } from "@ant-design/icons";
import { Layout, Space, Typography } from "antd";

const { Header } = Layout;
const { Title } = Typography;

export const AppHeader = () => {
  return (
    <Header className="bg-white border-b border-gray-200 px-6 md:px-8 h-16 flex items-center w-full flex-shrink-0">
      <div className="flex items-center justify-between h-full w-full max-w-7xl mx-auto">
        <Space size="large">
          <Title level={3} className="!mb-0 flex items-center gap-2 text-gray-900">
            <LineChartOutlined className="text-blue-600" />
            Biormika HFO Detector
          </Title>
        </Space>
        <div className="text-sm text-gray-500 hidden md:block">
          Real-time High-Frequency Oscillation Detection
        </div>
      </div>
    </Header>
  );
};