"use client";

import React, { useState, useCallback } from "react";
import { Card, Input, Alert, Typography, Space, Spin, Button } from "antd";
import { FileTextOutlined, CloseOutlined, CheckOutlined } from "@ant-design/icons";

const { Title, Text } = Typography;

interface FileUploadCardProps {
  onFileSelect: (filepath: string) => void;
  error?: string;
  isLoading?: boolean;
}

const FileUploadCard: React.FC<FileUploadCardProps> = ({ onFileSelect, error, isLoading = false }) => {
  const [filepath, setFilepath] = useState("");
  const [localError, setLocalError] = useState("");
  const [hasValidated, setHasValidated] = useState(false);

  // Client-side validation
  const validatePath = (path: string): string | null => {
    if (!path.trim()) {
      return "Please enter a file path";
    }
    
    if (!path.toLowerCase().endsWith('.edf')) {
      return "File must have .edf extension";
    }
    
    // Basic path validation - check for invalid characters
    const invalidChars = /[<>"|?*]/;
    if (invalidChars.test(path)) {
      return "Path contains invalid characters";
    }
    
    return null;
  };

  const handleInputChange = (value: string) => {
    setFilepath(value);
    setLocalError("");
    setHasValidated(false);
    // Clear any previous errors when user starts typing
    if (!value) {
      onFileSelect("");
    }
  };

  const handleValidate = useCallback(() => {
    const validationError = validatePath(filepath);
    
    if (validationError) {
      setLocalError(validationError);
      setHasValidated(true);
      return;
    }
    
    setLocalError("");
    setHasValidated(true);
    // Only call the API if client-side validation passes
    onFileSelect(filepath);
  }, [filepath, onFileSelect]);

  const clearInput = () => {
    setFilepath("");
    setLocalError("");
    setHasValidated(false);
    onFileSelect("");
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && filepath && !isLoading) {
      handleValidate();
    }
  };

  return (
    <div className="flex-1 flex items-center justify-center p-6 bg-gray-50">
      <Card
        className="max-w-4xl w-full shadow-lg border border-gray-200"
        styles={{
          body: { padding: "32px" },
        }}
      >
        <Spin spinning={isLoading}>
          <Space direction="vertical" size="large" className="w-full">
            {/* Header */}
            <div className="text-center mb-6">
              <Title level={2} className="!mb-3 text-gray-900">
                File Selection
              </Title>
              <Text type="secondary" className="text-lg">
                Enter the full path to your EDF file
              </Text>
            </div>

            {/* File Path Input */}
            <div className="mb-6">
              <Text strong className="block mb-3 text-gray-800 text-base">
                Enter complete file path:
              </Text>
              <Space.Compact style={{ width: '100%' }} size="large">
                <Input
                  size="large"
                  value={filepath}
                  onChange={(e) => handleInputChange(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="/path/to/file.edf"
                  disabled={isLoading}
                  prefix={<FileTextOutlined className="text-gray-400" />}
                  suffix={
                    filepath && !isLoading ? (
                      <CloseOutlined className="cursor-pointer text-gray-400 hover:text-gray-600 transition-colors" onClick={clearInput} />
                    ) : null
                  }
                  className="!py-3 text-base"
                  status={localError ? "error" : undefined}
                />
                <Button
                  type="primary"
                  size="large"
                  icon={<CheckOutlined />}
                  onClick={handleValidate}
                  disabled={!filepath || isLoading}
                  loading={isLoading}
                >
                  Validate
                </Button>
              </Space.Compact>
              <Text type="secondary" className="text-sm mt-2 block">
                Example: /Users/<USER>/Documents/patient_data.edf
              </Text>
            </div>

            {/* Error Alerts */}
            {localError && (
              <Alert 
                message="Validation Error" 
                description={localError} 
                type="warning" 
                showIcon 
                closable
                onClose={() => setLocalError("")}
              />
            )}
            
            {error && !localError && (
              <Alert 
                message="File Error" 
                description={error} 
                type="error" 
                showIcon 
                closable 
              />
            )}

            {/* File Validated Successfully */}
            {hasValidated && !error && !localError && filepath && (
              <Alert
                message="File Validated Successfully"
                description={
                  <Space>
                    <FileTextOutlined />
                    <Text code>{filepath}</Text>
                  </Space>
                }
                type="success"
                showIcon
              />
            )}

            {/* Instructions */}
            <Card
              type="inner"
              className="bg-blue-50 border-blue-200 mt-6"
              styles={{
                body: { padding: "20px" },
              }}
            >
              <Space direction="vertical" size="small" className="w-full">
                <Text strong className="text-blue-800 text-base">
                  Requirements:
                </Text>
                <ul className="list-disc list-inside text-sm text-blue-700 space-y-2 ml-2">
                  <li>Provide the complete file path to your EDF file</li>
                  <li>File must be in EDF format (.edf extension)</li>
                  <li>File must be accessible from the backend server</li>
                  <li>Minimum sampling rate of 200 Hz required</li>
                  <li>File should contain valid EEG channel data</li>
                </ul>
              </Space>
            </Card>

            {/* Action Button - shown only after successful validation */}
            {hasValidated && filepath && !isLoading && !error && !localError && (
              <Button
                type="primary"
                size="large"
                block
                className="!bg-blue-600 !border-blue-600 hover:!bg-blue-700 !h-12 !text-lg !font-semibold mt-6"
              >
                Continue to Parameter Settings
              </Button>
            )}
          </Space>
        </Spin>
      </Card>
    </div>
  );
};

export default FileUploadCard;
