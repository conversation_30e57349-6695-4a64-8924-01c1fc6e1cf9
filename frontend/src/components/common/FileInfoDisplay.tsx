import React from "react";
import { <PERSON><PERSON>, Row, Col, Typography, Tag } from "antd";
import { FileInfo } from "@/types/eeg";
import { InfoCircleOutlined, WarningOutlined } from "@ant-design/icons";

const { Text } = Typography;

interface FileInfoDisplayProps {
  fileInfo: FileInfo;
  montageType?: 'bipolar' | 'average' | 'referential';
}

const FileInfoDisplay: React.FC<FileInfoDisplayProps> = ({ fileInfo, montageType }) => {
  const formatDuration = (seconds: number) => {
    if (!seconds || seconds === 0) return "N/A";
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  };

  const getMontageLabel = (type?: string) => {
    switch (type) {
      case 'bipolar':
        return 'Bipolar';
      case 'average':
        return 'Average Reference';
      case 'referential':
        return 'Referential';
      default:
        return 'Not selected';
    }
  };

  return (
    <>
      <Alert
        message={
          <div className="flex items-center justify-between">
            <span>File Information</span>
            {fileInfo?.validationWarnings && fileInfo.validationWarnings.length > 0 && (
              <Tag icon={<WarningOutlined />} color="warning">
                {fileInfo.validationWarnings.length} validation warning{fileInfo.validationWarnings.length > 1 ? 's' : ''} found
              </Tag>
            )}
          </div>
        }
        description={
          <Row gutter={[16, 8]} className="mt-2">
            <Col span={8}>
              <Text strong>Filename:</Text> <Text code>{fileInfo?.filename || "N/A"}</Text>
            </Col>
            <Col span={8}>
              <Text strong>Duration:</Text> <Text>{formatDuration(fileInfo?.durationSeconds)}</Text>
            </Col>
            <Col span={8}>
              <Text strong>Channels:</Text> <Text>{fileInfo?.channels?.length || 0}</Text>
            </Col>
            <Col span={8}>
              <Text strong>Sampling Rate:</Text> <Text>{fileInfo?.samplingRate || "N/A"} Hz</Text>
            </Col>
            <Col span={8}>
              <Text strong>Start:</Text>{" "}
              <Text>
                {fileInfo?.startDate || ""} {fileInfo?.startTime || ""}
              </Text>
            </Col>
            <Col span={8}>
              <Text strong>Montage:</Text> <Text>{getMontageLabel(montageType)}</Text>
            </Col>
          </Row>
        }
        type="info"
        showIcon
      />
      
      {fileInfo?.validationWarnings && fileInfo.validationWarnings.length > 0 && (
        <Alert
          message="File Validation Warnings"
          description={
            <ul className="list-disc list-inside space-y-1">
              {fileInfo.validationWarnings.map((warning, index) => (
                <li key={index} className="text-sm">
                  {warning}
                </li>
              ))}
            </ul>
          }
          type="warning"
          showIcon
          icon={<WarningOutlined />}
          className="mt-3"
        />
      )}
    </>
  );
};

export default FileInfoDisplay;