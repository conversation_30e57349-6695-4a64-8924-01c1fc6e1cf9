import React from "react";
import { Alert } from "antd";

interface ErrorAlertProps {
  errors: string[];
  title?: string;
}

const ErrorAlert: React.FC<ErrorAlertProps> = ({ errors, title = "Validation Errors" }) => {
  if (!errors || errors.length === 0) return null;

  return (
    <Alert
      message={title}
      description={
        <ul className="list-disc list-inside">
          {errors.map((error, index) => (
            <li key={index}>{error}</li>
          ))}
        </ul>
      }
      type="error"
      showIcon
    />
  );
};

export default ErrorAlert;