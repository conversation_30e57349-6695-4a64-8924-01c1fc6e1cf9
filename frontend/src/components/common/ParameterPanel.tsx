import React from "react";
import { Space, Typography, Tag } from "antd";
import { CheckCircleOutlined, ExclamationCircleOutlined } from "@ant-design/icons";
import { ValidationErrors } from "@/types/eeg";

const { Text } = Typography;

interface ParameterPanelProps {
  title: string;
  sectionKey: keyof ValidationErrors;
  errors?: string[];
  children?: React.ReactNode;
  isLabel?: boolean;
}

const ParameterPanel: React.FC<ParameterPanelProps> = ({ title, errors = [], children, isLabel = false }) => {
  const hasErrors = errors && errors.length > 0;
  const status = hasErrors ? "error" : "valid";

  const renderHeader = () => (
    <Space>
      {status === "error" ? (
        <ExclamationCircleOutlined className="text-red-500" />
      ) : (
        <CheckCircleOutlined className="text-green-500" />
      )}
      <Text strong>{title}</Text>
      {hasErrors && (
        <Tag color="red">
          {errors.length} error{errors.length > 1 ? "s" : ""}
        </Tag>
      )}
    </Space>
  );

  // If used as a label for Collapse items, just return the header
  if (isLabel) {
    return renderHeader();
  }

  // Otherwise, return wrapped with children (for backwards compatibility)
  return (
    <div className={hasErrors ? "error-panel" : ""}>
      {renderHeader()}
      {children}
    </div>
  );
};

export default ParameterPanel;