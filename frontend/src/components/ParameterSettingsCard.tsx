"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, Collapse, Button, Space, Alert, Typography, Divider, Row, Col, message } from "antd";
import {
  SettingOutlined,
  ArrowLeftOutlined,
  PlayCircleOutlined,
  ReloadOutlined,
} from "@ant-design/icons";
import { AnalysisParameters, FileInfo, ValidationErrors, DEFAULT_PARAMETERS } from "@/types/eeg";
import { validateAllParameters, areParametersValid } from "@/utils/validation";
import ThresholdSection from "./ParameterSections/ThresholdSection";
import MontageSection from "./ParameterSections/MontageSection";
import FrequencySection from "./ParameterSections/FrequencySection";
import TimeSegmentSection from "./ParameterSections/TimeSegmentSection";
import ChannelSelectionSection from "./ParameterSections/ChannelSelectionSection";
import FileInfoDisplay from "./common/FileInfoDisplay";
import ParameterPanel from "./common/ParameterPanel";
import ErrorAlert from "./common/ErrorAlert";

const { Title, Text } = Typography;

interface ParameterSettingsCardProps {
  fileInfo: FileInfo;
  onParametersChange: (parameters: AnalysisParameters) => void;
  onValidationChange: (isValid: boolean, errors: ValidationErrors) => void;
  onStartAnalysis: () => void;
  onBack: () => void;
  initialParameters?: AnalysisParameters;
}

const ParameterSettingsCard: React.FC<ParameterSettingsCardProps> = ({
  fileInfo,
  onParametersChange,
  onValidationChange,
  onStartAnalysis,
  onBack,
  initialParameters = DEFAULT_PARAMETERS,
}) => {
  const [parameters, setParameters] = useState<AnalysisParameters>(initialParameters);
  const [validationErrors, setValidationErrors] = useState<ValidationErrors>({});
  const [activeKeys, setActiveKeys] = useState<string[]>(["thresholds"]);

  // Sync with initialParameters when they change
  useEffect(() => {
    setParameters(initialParameters);
  }, [initialParameters]);

  // Initialize with enhanced defaults when fileInfo is available
  useEffect(() => {
    if (fileInfo && fileInfo.channels && parameters.channelSelection.selectedLeads.length === 0) {
      const enhancedDefaults = {
        ...parameters,
        channelSelection: {
          ...parameters.channelSelection,
          selectedLeads: fileInfo.channels?.slice(0, Math.min(10, fileInfo.channels.length)) || [],
        },
      };
      setParameters(enhancedDefaults);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fileInfo]); // Only run when fileInfo changes, parameters excluded intentionally

  // Validate parameters whenever they change
  useEffect(() => {
    const errors = validateAllParameters(parameters, fileInfo);
    setValidationErrors(errors);
    const isValid = areParametersValid(errors);
    onValidationChange(isValid, errors);
    onParametersChange(parameters);
  }, [parameters, fileInfo, onValidationChange, onParametersChange]);

  const updateParameters = (updates: Partial<AnalysisParameters>) => {
    // Check for montage-specific validations and show toast messages
    if (updates.montage) {
      if (updates.montage.type === 'bipolar') {
        // Check if there are enough channels selected
        const selectedChannels = parameters.channelSelection.selectedLeads.length > 0 
          ? parameters.channelSelection.selectedLeads.length 
          : fileInfo.channels?.length || 0;
        
        if (selectedChannels < 2) {
          message.warning({
            content: '⚠️ Bipolar montage requires at least 2 channels. Please ensure enough channels are selected.',
            duration: 4,
          });
        }
      }
    }
    
    setParameters((prev) => ({ ...prev, ...updates }));
  };

  const resetToDefaults = () => {
    // Create enhanced defaults with file-specific information
    const enhancedDefaults = {
      ...DEFAULT_PARAMETERS,
      channelSelection: {
        ...DEFAULT_PARAMETERS.channelSelection,
        selectedLeads: fileInfo.channels?.slice(0, Math.min(10, fileInfo.channels.length)) || [],
      },
    };
    setParameters(enhancedDefaults);
    message.success("Parameters reset to default values with file-specific settings");
  };

  const isValid = areParametersValid(validationErrors);
  const hasErrors = Object.keys(validationErrors).length > 0;

  const getSectionStatus = (sectionKey: keyof ValidationErrors) => {
    return validationErrors[sectionKey] ? "error" : "valid";
  };

  return (
    <Card
      className=""
      styles={{
        body: { padding: "32px" },
      }}
    >
      <Space direction="vertical" size="large" className="w-full">
        {/* Header */}
        <div>
          <Space align="center" className="mb-4">
            <SettingOutlined style={{ fontSize: 24 }} />
            <Title level={2} className="!mb-0">
              Analysis Parameters Configuration
            </Title>
          </Space>
          <Text type="secondary">Configure the parameters for HFO detection analysis</Text>
        </div>

        {/* Status Message */}
        {fileInfo?.statusMessage && (
          <Alert
            message="File Status"
            description={fileInfo.statusMessage}
            type="success"
            showIcon
          />
        )}

        {/* File Info */}
        <FileInfoDisplay fileInfo={fileInfo} montageType={parameters.montage.type} />

        {/* Validation Status */}
        {hasErrors && (
          <div>
            <Text>Please resolve all validation errors before starting the analysis.</Text>
            <ErrorAlert
              errors={Object.values(validationErrors).flat()}
              title="Validation Status"
            />
          </div>
        )}

        {/* Parameter Sections in Collapse */}
        <Collapse 
          activeKey={activeKeys} 
          onChange={setActiveKeys} 
          size="large" 
          className="parameter-collapse"
          items={[
            {
              key: "thresholds",
              label: <ParameterPanel
                title="Threshold Parameters"
                sectionKey="thresholds"
                errors={validationErrors.thresholds}
                isLabel={true}
              />,
              children: (
                <ThresholdSection
                  parameters={parameters.thresholds}
                  onUpdate={(thresholds) => updateParameters({ thresholds })}
                  errors={validationErrors.thresholds || []}
                  isExpanded={true}
                  onToggle={() => {}}
                  status={getSectionStatus("thresholds")}
                />
              ),
            },
            {
              key: "montage",
              label: <ParameterPanel
                title="Montage Configuration"
                sectionKey="montage"
                errors={validationErrors.montage}
                isLabel={true}
              />,
              children: (
                <MontageSection
                  parameters={parameters.montage}
                  onUpdate={(montage) => updateParameters({ montage })}
                  errors={validationErrors.montage || []}
                  isExpanded={true}
                  onToggle={() => {}}
                  status={getSectionStatus("montage")}
                  availableChannels={fileInfo.channels}
                />
              ),
            },
            {
              key: "frequency",
              label: <ParameterPanel
                title="Frequency Bands"
                sectionKey="frequency"
                errors={validationErrors.frequency}
                isLabel={true}
              />,
              children: (
                <FrequencySection
                  parameters={parameters.frequency}
                  onUpdate={(frequency) => updateParameters({ frequency })}
                  errors={validationErrors.frequency || []}
                  isExpanded={true}
                  onToggle={() => {}}
                  status={getSectionStatus("frequency")}
                  fileInfo={fileInfo}
                  validationWarnings={fileInfo?.validationWarnings}
                />
              ),
            },
            {
              key: "timeSegment",
              label: <ParameterPanel
                title="Time Segment"
                sectionKey="timeSegment"
                errors={validationErrors.timeSegment}
                isLabel={true}
              />,
              children: (
                <TimeSegmentSection
                  parameters={parameters.timeSegment}
                  onUpdate={(timeSegment) => updateParameters({ timeSegment })}
                  errors={validationErrors.timeSegment || []}
                  isExpanded={true}
                  onToggle={() => {}}
                  status={getSectionStatus("timeSegment")}
                  fileDuration={fileInfo.durationSeconds}
                />
              ),
            },
            {
              key: "channelSelection",
              label: <ParameterPanel
                title="Channel Selection"
                sectionKey="channelSelection"
                errors={validationErrors.channelSelection}
                isLabel={true}
              />,
              children: (
                <ChannelSelectionSection
                  parameters={parameters.channelSelection}
                  onUpdate={(channelSelection) => updateParameters({ channelSelection })}
                  errors={validationErrors.channelSelection || []}
                  isExpanded={true}
                  onToggle={() => {}}
                  status={getSectionStatus("channelSelection")}
                  availableChannels={fileInfo?.channels || []}
                  montageType={parameters.montage.type}
                  validationWarnings={fileInfo?.validationWarnings}
                />
              ),
            },
          ]}
        />

        <Divider />

        {/* Action Buttons */}
        <Row gutter={16}>
          <Col span={6}>
            <Button type="default" size="large" block icon={<ArrowLeftOutlined />} onClick={onBack}>
              Back to File Selection
            </Button>
          </Col>
          <Col span={6}>
            <Button type="default" size="large" block icon={<ReloadOutlined />} onClick={resetToDefaults}>
              Reset to Defaults
            </Button>
          </Col>
          <Col span={12}>
            <Button
              type="primary"
              size="large"
              block
              icon={<PlayCircleOutlined />}
              onClick={onStartAnalysis}
              className="!bg-black !border-black hover:!bg-gray-800 disabled:!bg-gray-300"
            >
              {isValid ? "Start Analysis" : "Start Analysis (will use defaults for invalid parameters)"}
            </Button>
          </Col>
        </Row>

        {/* Help Text */}
        <Alert
          message="Tips"
          description={
            <ul className="list-disc list-inside space-y-1">
              <li>All sections must be valid (green checkmark) before analysis can begin</li>
              <li>Click on each section to expand and configure parameters</li>
              <li>Hover over parameter labels for detailed descriptions</li>
              <li>Use &quot;Reset to Defaults&quot; to restore recommended settings</li>
            </ul>
          }
          type="info"
          showIcon
          className="mt-4"
        />
      </Space>
    </Card>
  );
};

export default ParameterSettingsCard;
