'use client';

import React, { useRef, useEffect } from 'react';
import ChannelRow from './ChannelRow';
import { HFOEvent } from '@/types/eeg';

interface ChannelGridProps {
  channelData: Record<string, number[]>;
  visibleChannels: string[];
  timeWindow: [number, number];
  samplingRate: number;
  showHFOMarkers?: boolean;
  hfoEvents?: HFOEvent[];
  channelHeight?: number;
}

const ChannelGrid: React.FC<ChannelGridProps> = ({
  channelData,
  visibleChannels,
  timeWindow,
  samplingRate,
  showHFOMarkers = false,
  hfoEvents = [],
  channelHeight = 80,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);

  const handleWheel = (e: WheelEvent) => {
    if (containerRef.current && e.shiftKey) {
      e.preventDefault();
      containerRef.current.scrollLeft += e.deltaY;
    }
  };

  useEffect(() => {
    const container = containerRef.current;
    if (container) {
      container.addEventListener('wheel', handleWheel, { passive: false });
      return () => {
        container.removeEventListener('wheel', handleWheel);
      };
    }
  }, []);

  if (visibleChannels.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center text-gray-500">
        <div className="text-center">
          <p className="text-sm">No channels selected</p>
          <p className="text-xs mt-1">Select channels from the panel on the right</p>
        </div>
      </div>
    );
  }

  return (
    <div 
      ref={containerRef}
      className="flex-1 overflow-y-auto overflow-x-hidden bg-white"
      style={{ maxHeight: 'calc(100vh - 128px)' }}
    >
      <div className="min-w-full">
        {visibleChannels.map((channel) => {
          const data = channelData[channel];
          if (!data || data.length === 0) {
            return null;
          }

          return (
            <ChannelRow
              key={channel}
              channelName={channel}
              data={data}
              timeWindow={timeWindow}
              samplingRate={samplingRate}
              height={channelHeight}
              showHFOMarkers={showHFOMarkers}
              hfoEvents={hfoEvents}
            />
          );
        })}
      </div>
      
      <div className="p-4 text-center text-xs text-gray-500">
        <p>Tip: Use scroll to navigate vertically, Shift+Scroll for horizontal navigation</p>
      </div>
    </div>
  );
};

export default ChannelGrid;