"use client";

import React, { useEffect, useState, useCallback, useRef } from "react";
import dynamic from "next/dynamic";
import { useWebSocket } from "@/contexts/WebSocketContext";
import ConnectionStatus from "@/components/ConnectionStatus";
import ProgressIndicator from "@/components/ProgressIndicator";
import ControlPanel from "./ControlPanel";
import { Layers, Download } from "lucide-react";
import clsx from "clsx";
import { HFOEvent } from "@/types/eeg";

const ChannelGrid = dynamic(() => import("./ChannelGrid"), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center h-96">
      <div className="text-center">
        <div className="animate-pulse">
          <Layers className="w-12 h-12 text-gray-400 mx-auto mb-3" />
        </div>
        <p className="text-sm text-gray-600">Loading visualization...</p>
      </div>
    </div>
  ),
});

const EEGViewer: React.FC = () => {
  const { isConnected, progress, chunkResults, error } = useWebSocket();
  const [timeWindow, setTimeWindow] = useState<[number, number]>([0, 10]);
  const [selectedChannels, setSelectedChannels] = useState<string[]>([]);

  // New state for controls
  const [timeWindowSize, setTimeWindowSize] = useState(10); // Default 10s window
  const [currentChunk, setCurrentChunk] = useState(0);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Ref for channel view container
  const channelViewRef = useRef<HTMLDivElement>(null);

  const allChannelData: Record<string, number[]> = {};
  const allHFOEvents: HFOEvent[] = [];

  chunkResults.forEach((chunk) => {
    if (chunk.channelData) {
      Object.entries(chunk.channelData).forEach(([channel, data]) => {
        if (!allChannelData[channel]) {
          allChannelData[channel] = [];
        }
        allChannelData[channel].push(...(data as number[]));
      });
    }

    // Collect HFO events from chunks
    if (chunk.hfoEvents && Array.isArray(chunk.hfoEvents)) {
      // HFO times are already absolute from file start (backend provides them this way)
      chunk.hfoEvents.forEach((hfo: HFOEvent) => {
        allHFOEvents.push(hfo);
      });

      // Debug logging for HFO events
      if (chunk.hfoEvents.length > 0) {
        console.log(`Chunk ${chunk.chunkNumber}: Found ${chunk.hfoEvents.length} HFOs`, chunk.hfoEvents);
      }
    }
  });

  const channels = Object.keys(allChannelData);
  const visibleChannels = selectedChannels.length > 0 ? selectedChannels : channels;

  // Debug: Log total HFOs collected
  if (allHFOEvents.length > 0) {
    console.log(`Total HFOs collected: ${allHFOEvents.length}`, allHFOEvents);
  }

  const totalDataPoints = Object.values(allChannelData).reduce((sum, data) => sum + data.length, 0);

  useEffect(() => {
    if (channels.length > 0 && selectedChannels.length === 0) {
      setSelectedChannels(channels.slice(0, Math.min(10, channels.length)));
    }
  }, [channels, selectedChannels.length]);

  // Calculate total duration from chunks
  const totalDuration = chunkResults.length > 0 ? Math.max(...chunkResults.map((c) => c.timeRange?.[1] || 0)) : 0;
  const totalChunks = Math.ceil(totalDuration / 10);

  // Navigation handlers
  const handleNavigate = useCallback(
    (direction: "prev" | "next") => {
      if (direction === "prev" && currentChunk > 0) {
        setCurrentChunk(currentChunk - 1);
        const newStart = (currentChunk - 1) * 10;
        setTimeWindow([newStart, newStart + timeWindowSize]);
      } else if (direction === "next" && currentChunk < totalChunks - 1) {
        setCurrentChunk(currentChunk + 1);
        const newStart = (currentChunk + 1) * 10;
        setTimeWindow([newStart, newStart + timeWindowSize]);
      }
    },
    [currentChunk, totalChunks, timeWindowSize]
  );

  // Time window change handler
  const handleTimeWindowChange = useCallback(
    (newSize: number) => {
      setTimeWindowSize(newSize);
      setTimeWindow([timeWindow[0], timeWindow[0] + newSize]);
    },
    [timeWindow]
  );

  // Reset handler - resets to default settings
  const handleReset = useCallback(() => {
    setTimeWindowSize(10);
    setTimeWindow([0, 10]);
    setCurrentChunk(0);
    if (channels.length > 0) {
      setSelectedChannels(channels.slice(0, Math.min(10, channels.length)));
    }
  }, [channels]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      // Prevent shortcuts when typing in input fields
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
        return;
      }

      switch (e.key.toLowerCase()) {
        case "a": // Zoom in time (decrease window)
          handleTimeWindowChange(Math.max(1, timeWindowSize / 2));
          break;
        case "d": // Zoom out time (increase window)
          handleTimeWindowChange(Math.min(30, timeWindowSize * 2));
          break;
        case "o": // Previous chunk
          handleNavigate("prev");
          break;
        case "p": // Next chunk
          handleNavigate("next");
          break;
        case "f": // Toggle fullscreen for channel view
          if (channelViewRef.current) {
            if (!document.fullscreenElement) {
              channelViewRef.current.requestFullscreen();
            } else {
              document.exitFullscreen();
            }
          }
          break;
      }
    };

    document.addEventListener("keydown", handleKeyPress);
    return () => document.removeEventListener("keydown", handleKeyPress);
  }, [timeWindowSize, handleTimeWindowChange, handleNavigate]);

  // Fullscreen change listener
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener("fullscreenchange", handleFullscreenChange);
    return () => document.removeEventListener("fullscreenchange", handleFullscreenChange);
  }, []);

  const toggleChannel = (channel: string) => {
    setSelectedChannels((prev) => (prev.includes(channel) ? prev.filter((ch) => ch !== channel) : [...prev, channel]));
  };

  return (
    <div className="flex flex-col h-full bg-gray-50">
      <header className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-xl font-bold text-gray-900">EEG Signal Analysis</h1>
            <p className="text-sm text-gray-600">Real-time High-Frequency Oscillation Detection</p>
          </div>
          <div className="flex items-center gap-3 w-[20%]">
            <ConnectionStatus isConnected={isConnected} progress={progress} dataPoints={totalDataPoints} error={error || undefined} compact />
          </div>
        </div>
        <div className="flex items-center justify-end gap-3 w-[20%]">
          <ProgressIndicator progress={progress} label="Analysis" variant="medical" size="small" />
        </div>
      </header>

      {channels.length > 0 ? (
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Add ControlPanel */}
          <div className="px-4 pt-4">
            <ControlPanel
              timeWindow={timeWindowSize}
              onTimeWindowChange={handleTimeWindowChange}
              currentChunk={currentChunk}
              totalChunks={totalChunks}
              onNavigate={handleNavigate}
              currentTimeRange={timeWindow}
              totalDuration={totalDuration}
              onReset={handleReset}
            />

            {/* Position indicator */}
            <div className="mt-2 bg-gray-100 rounded p-2">
              <div className="relative h-2 bg-gray-300 rounded">
                <div
                  className="absolute h-full bg-black rounded transition-all duration-300"
                  style={{
                    left: `${(timeWindow[0] / totalDuration) * 100}%`,
                    width: `${((timeWindow[1] - timeWindow[0]) / totalDuration) * 100}%`,
                  }}
                />
              </div>
              <div className="flex justify-between text-xs text-gray-600 mt-1">
                <span>0s</span>
                <span className="font-semibold">
                  {timeWindow[0].toFixed(1)}s - {timeWindow[1].toFixed(1)}s
                </span>
                <span>{totalDuration.toFixed(1)}s</span>
              </div>
            </div>
          </div>

          <div ref={channelViewRef} className={clsx("flex-1 flex overflow-hidden bg-white rounded-lg shadow-sm", isFullscreen ? "m-0" : "m-4")}>
            <div className="flex-1 flex flex-col">
              <div className="flex-shrink-0 h-14 bg-gray-50 border-b border-gray-200 px-4 flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <h2 className="text-sm font-semibold text-gray-900">Channel View</h2>
                  <span className="text-xs text-gray-600 bg-white px-2 py-1 rounded">
                    {visibleChannels.length}/{channels.length} channels
                  </span>
                </div>

                <div className="flex items-center gap-2">
                  {isFullscreen && (
                    <button
                      onClick={() => document.exitFullscreen()}
                      className="px-3 py-1 bg-gray-700 text-white text-xs rounded hover:bg-gray-600 transition-colors"
                    >
                      Exit Fullscreen (F)
                    </button>
                  )}
                  <button className="px-3 py-1 bg-black text-white text-xs rounded hover:bg-gray-800 transition-colors flex items-center gap-1">
                    <Download className="w-3 h-3" />
                    Export
                  </button>
                </div>
              </div>

              <div className="flex-1 overflow-auto">
                <ChannelGrid
                  channelData={allChannelData}
                  visibleChannels={visibleChannels}
                  timeWindow={timeWindow}
                  samplingRate={256}
                  showHFOMarkers={true}
                  hfoEvents={allHFOEvents}
                  channelHeight={80}
                />
              </div>
            </div>

            {!isFullscreen && (
              <div className="flex-shrink-0 w-64 bg-gray-50 border-l border-gray-200 overflow-hidden flex flex-col">
                <div className="p-3 border-b border-gray-200">
                  <h3 className="text-xs font-semibold text-gray-900 flex items-center gap-2">
                    <Layers className="w-3 h-3 text-gray-600" />
                    Channel Selection
                  </h3>
                </div>
                <div className="mt-3 pt-3 border-t border-gray-200">
                  <button onClick={() => setSelectedChannels(channels)} className="w-full text-xs text-gray-700 hover:text-black font-medium">
                    Select All
                  </button>
                  <button onClick={() => setSelectedChannels([])} className="w-full text-xs text-gray-600 hover:text-gray-700 font-medium mt-1">
                    Clear Selection
                  </button>
                </div>

                <div className="flex-1 overflow-y-auto p-3">
                  <div className="space-y-1">
                    {channels.map((channel) => (
                      <label
                        key={channel}
                        className={clsx(
                          "flex items-center gap-2 p-1.5 rounded cursor-pointer transition-all",
                          "hover:bg-white",
                          selectedChannels.includes(channel) && "bg-white border border-gray-300"
                        )}
                      >
                        <input
                          type="checkbox"
                          checked={selectedChannels.includes(channel)}
                          onChange={() => toggleChannel(channel)}
                          className="w-3 h-3 text-gray-700 border-gray-300 rounded focus:ring-gray-500"
                        />
                        <div className="flex-1">
                          <span className="text-xs font-medium text-gray-700">{channel}</span>
                          <div
                            className="h-1 mt-1 rounded-full"
                            style={{
                              background: selectedChannels.includes(channel) ? "#000000" : "#d1d5db",
                            }}
                          />
                        </div>
                      </label>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      ) : (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center bg-white rounded-lg shadow-sm p-8">
            <div className="animate-pulse mb-4">
              <Layers className="w-16 h-16 text-gray-400 mx-auto" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">{isConnected ? "Waiting for EEG Data" : "Connecting to Server"}</h3>
            <p className="text-sm text-gray-600">
              {isConnected ? "The analysis will begin shortly. Please wait..." : "Establishing WebSocket connection..."}
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default EEGViewer;
