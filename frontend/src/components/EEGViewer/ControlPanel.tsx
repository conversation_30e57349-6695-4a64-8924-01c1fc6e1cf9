'use client';

import React from 'react';
import { Card, Row, Col, Slider, Button, Space, Typography, Divider } from 'antd';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;

interface ControlPanelProps {
  // Time window control
  timeWindow: number;  // seconds visible
  onTimeWindowChange: (window: number) => void;
  
  // Navigation
  currentChunk: number;
  totalChunks: number;
  onNavigate: (direction: 'prev' | 'next') => void;
  
  // Display info
  currentTimeRange: [number, number];
  totalDuration: number;
  
  // Reset
  onReset: () => void;
}

const ControlPanel: React.FC<ControlPanelProps> = ({
  timeWindow,
  onTimeWindowChange,
  currentChunk,
  totalChunks,
  onNavigate,
  currentTimeRange,
  totalDuration,
  onReset,
}) => {
  // Preset time window values
  const timeWindowPresets = [1, 5, 10, 30];
  
  // Format time display
  const formatTime = (seconds: number): string => {
    if (seconds < 60) {
      return `${seconds.toFixed(1)}s`;
    }
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${minutes}m ${secs.toFixed(0)}s`;
  };

  return (
    <Card className="control-panel mb-4" size="small">
      <Row gutter={[16, 16]} align="middle">
        {/* Time Window Control */}
        <Col xs={24} sm={24} md={8}>
          <div className="control-section">
            <Title level={5} className="!mb-2">Time Window</Title>
            <Space size="small" wrap className="mb-2">
              {timeWindowPresets.map(preset => (
                <Button
                  key={preset}
                  size="small"
                  type={timeWindow === preset ? 'primary' : 'default'}
                  onClick={() => onTimeWindowChange(preset)}
                >
                  {preset}s
                </Button>
              ))}
            </Space>
            <div>
              <Text type="secondary">Current view: {timeWindow} seconds</Text>
            </div>
          </div>
        </Col>
        
        {/* Navigation */}
        <Col xs={24} sm={24} md={8}>
          <div className="control-section">
            <Title level={5} className="!mb-2">Navigation</Title>
            <Space size="small" wrap className="mb-2">
              <Button
                size="small"
                icon={<LeftOutlined />}
                onClick={() => onNavigate('prev')}
                disabled={currentChunk === 0}
              >
                Previous
              </Button>
              <Button
                size="small"
                icon={<RightOutlined />}
                onClick={() => onNavigate('next')}
                disabled={currentChunk >= totalChunks - 1}
                iconPosition="end"
              >
                Next
              </Button>
              <Button
                size="small"
                onClick={onReset}
                type="dashed"
              >
                Reset
              </Button>
            </Space>
            <div>
              <Text strong>
                {formatTime(currentTimeRange[0])} - {formatTime(currentTimeRange[1])}
              </Text>
              <Text type="secondary"> of {formatTime(totalDuration)} total</Text>
            </div>
            <div>
              <Text type="secondary" className="text-xs">
                Chunk {currentChunk + 1} of {totalChunks}
              </Text>
            </div>
          </div>
        </Col>
      </Row>
      
      {/* Keyboard shortcuts help */}
      <Divider className="!my-3" />
      <div className="text-center">
        <Text type="secondary" className="text-xs">
          <strong>Keyboard Shortcuts:</strong> A/D (time zoom) | O/P (navigate) | F (fullscreen)
        </Text>
      </div>
    </Card>
  );
};

export default ControlPanel;