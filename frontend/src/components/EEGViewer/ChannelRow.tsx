'use client';

import React, { useMemo } from 'react';
import Plot from 'react-plotly.js';
import { HFOEvent } from '@/types/eeg';
import { PlotlyTrace } from '@/types/plotly';

interface ChannelRowProps {
  channelName: string;
  data: number[];
  timeWindow: [number, number];
  samplingRate: number;
  height?: number;
  showHFOMarkers?: boolean;
  hfoEvents?: HFOEvent[];
}

const ChannelRow: React.FC<ChannelRowProps> = ({
  channelName,
  data,
  timeWindow,
  samplingRate,
  height = 80,
  showHFOMarkers = false,
  hfoEvents = [],
}) => {
  const plotData = useMemo(() => {
    if (!data || data.length === 0) {
      return [];
    }

    const timeAxis = data.map((_, i) => i / samplingRate);
    
    const startIdx = Math.floor(timeWindow[0] * samplingRate);
    const endIdx = Math.floor(timeWindow[1] * samplingRate);
    const windowedTime = timeAxis.slice(startIdx, endIdx);
    const windowedData = data.slice(startIdx, endIdx);

    const traces: PlotlyTrace[] = [{
      x: windowedTime,
      y: windowedData,
      type: 'scatter',
      mode: 'lines',
      name: channelName,
      line: { color: '#000000', width: 0.8 },
      hovertemplate: `Time: %{x:.2f}s<br>Amplitude: %{y:.2f}μV<extra></extra>`,
    }];

    return traces;
  }, [data, channelName, timeWindow, samplingRate]);

  const layout = useMemo(() => {
    // Create shapes for HFO markers (vertical lines)
    const shapes: Partial<Plotly.Shape>[] = [];
    
    if (showHFOMarkers) {
      const channelHFOs = hfoEvents.filter(
        hfo => hfo.channel === channelName && 
        hfo.startTime >= timeWindow[0] && 
        hfo.startTime <= timeWindow[1]
      );
      
      // Debug logging for HFOs in this channel
      if (channelHFOs.length > 0) {
        console.log(`Channel ${channelName}: ${channelHFOs.length} HFOs in window [${timeWindow[0]}, ${timeWindow[1]}]`, channelHFOs);
      }

      channelHFOs.forEach(hfo => {
        // Determine color based on HFO type
        let color = '#ef4444'; // Default red for accepted
        if (hfo.type === 'rejected' || hfo.type === 'rejected_long') {
          color = '#3b82f6'; // Blue for rejected
        }
        
        // Add vertical line at HFO start time
        shapes.push({
          type: 'line',
          x0: hfo.startTime,
          x1: hfo.startTime,
          y0: 0,
          y1: 1,
          xref: 'x',  // Explicitly specify x-axis reference
          yref: 'paper',
          line: {
            color: color,
            width: 1.5,
            dash: hfo.type === 'rejected_long' ? 'dot' : 'solid'
          },
          opacity: 0.7
        });
        
        // Optionally add end marker for longer HFOs
        if (hfo.endTime && hfo.endTime - hfo.startTime > 0.001) {
          shapes.push({
            type: 'rect',
            x0: hfo.startTime,
            x1: hfo.endTime,
            y0: 0,
            y1: 1,
            xref: 'x',  // Explicitly specify x-axis reference
            yref: 'paper',
            fillcolor: color,
            opacity: 0.1,
            line: {
              width: 0
            }
          });
        }
      });
    }
    
    return {
      xaxis: {
        range: timeWindow,
        showgrid: false,
        showticklabels: false,
        zeroline: false,
        showline: false,
      },
      yaxis: {
        showgrid: false,
        showticklabels: false,
        zeroline: false,
        showline: false,
        autorange: true,
      },
      height: height,
      margin: { l: 80, r: 10, t: 5, b: 5 },
      hovermode: 'x' as const,
      showlegend: false,
      plot_bgcolor: '#ffffff',
      paper_bgcolor: '#ffffff',
      shapes: shapes,
      annotations: [{
        x: 0,
        y: 0.5,
        xref: 'paper' as const,
        yref: 'paper' as const,
        text: channelName,
        showarrow: false,
        xanchor: 'right' as const,
        xshift: -10,
        font: {
          size: 11,
          color: '#4a5568',
          family: 'var(--font-sans), system-ui, -apple-system, sans-serif',
        },
      }],
    };
  }, [channelName, timeWindow, height, showHFOMarkers, hfoEvents]);

  const config = {
    displayModeBar: false,
    responsive: true,
    staticPlot: false,
  };

  return (
    <div className="border-b border-gray-100 hover:bg-gray-50 transition-colors">
      <Plot
        data={plotData}
        layout={layout}
        config={config}
        style={{ width: '100%', height: `${height}px` }}
      />
    </div>
  );
};

export default React.memo(ChannelRow);