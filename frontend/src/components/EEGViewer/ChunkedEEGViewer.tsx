import React, { useState, useRef, useMemo, useEffect } from "react";
import { Card, Space, Button, Select, Progress, Alert, Switch, Tooltip } from "antd";
import {
  FullscreenOutlined,
  FullscreenExitOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  ThunderboltOutlined,
  InfoCircleOutlined,
} from "@ant-design/icons";
import dynamic from "next/dynamic";
import { useChunkedWebSocket } from "@/contexts/ChunkedWebSocketContext";
import { HFOEvent } from "@/types/eeg";

const ChannelGrid = dynamic(() => import("./ChannelGrid").then((mod) => mod.ChannelGrid), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center h-64">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-500">Loading visualization...</p>
      </div>
    </div>
  ),
});

export const ChunkedEEGViewer: React.FC = () => {
  const { isConnected, progress, chunkResults, hfoEvents, error, isInitialized, totalChunks, chunkDuration, useChunkedMode, setUseChunkedMode } =
    useChunkedWebSocket();

  const [timeWindow, setTimeWindow] = useState<[number, number]>([0, 10]);
  const [selectedChannels, setSelectedChannels] = useState<string[]>([]);
  const [timeWindowSize, setTimeWindowSize] = useState(10);
  const [currentChunk, setCurrentChunk] = useState(0);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const channelViewRef = useRef<HTMLDivElement>(null);

  // Compile channel data from chunks
  const { allChannelData, channels } = useMemo(() => {
    const channelData: Record<string, number[]> = {};

    chunkResults.forEach((chunk) => {
      if (chunk.channelData) {
        Object.entries(chunk.channelData).forEach(([channel, data]) => {
          if (!channelData[channel]) {
            channelData[channel] = [];
          }
          channelData[channel].push(...(data as number[]));
        });
      }
    });

    return {
      allChannelData: channelData,
      channels: Object.keys(channelData),
    };
  }, [chunkResults]);

  const visibleChannels = selectedChannels.length > 0 ? selectedChannels : channels;

  // Calculate total duration based on processed chunks
  const totalDuration = useMemo(() => {
    if (chunkResults.length === 0) return 0;
    const lastChunk = chunkResults[chunkResults.length - 1];
    return lastChunk.timeRange?.[1] || 0;
  }, [chunkResults]);

  // Filter HFOs for current time window
  const visibleHFOs = useMemo(() => {
    return hfoEvents.filter((hfo) => hfo.startTime >= timeWindow[0] && hfo.endTime <= timeWindow[1]);
  }, [hfoEvents, timeWindow]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.key === "ArrowRight") {
        handleTimeWindowChange("next");
      } else if (e.key === "ArrowLeft") {
        handleTimeWindowChange("prev");
      }
    };

    window.addEventListener("keydown", handleKeyPress);
    return () => window.removeEventListener("keydown", handleKeyPress);
  }, [timeWindow, totalDuration, timeWindowSize]);

  const handleTimeWindowChange = (direction: "next" | "prev") => {
    const [start, end] = timeWindow;
    const step = timeWindowSize;

    if (direction === "next" && end < totalDuration) {
      const newStart = Math.min(start + step, totalDuration - timeWindowSize);
      setTimeWindow([newStart, newStart + timeWindowSize]);
    } else if (direction === "prev" && start > 0) {
      const newStart = Math.max(0, start - step);
      setTimeWindow([newStart, newStart + timeWindowSize]);
    }
  };

  const handleTimeWindowSizeChange = (size: number) => {
    setTimeWindowSize(size);
    const [start] = timeWindow;
    setTimeWindow([start, Math.min(start + size, totalDuration)]);
  };

  const toggleFullscreen = () => {
    if (!document.fullscreenElement && channelViewRef.current) {
      channelViewRef.current.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  if (error) {
    return (
      <Card title="EEG Viewer" className="h-full">
        <Alert message="Error" description={error} type="error" showIcon />
      </Card>
    );
  }

  if (!isConnected) {
    return (
      <Card title="EEG Viewer" className="h-full">
        <div className="flex items-center justify-center h-64">
          <p className="text-gray-500">Waiting for connection...</p>
        </div>
      </Card>
    );
  }

  return (
    <Card
      title={
        <div className="flex items-center justify-between">
          <span>EEG Viewer</span>
          <div className="flex items-center gap-4">
            <Tooltip title="Toggle between chunked and regular processing">
              <div className="flex items-center gap-2">
                <span className="text-sm font-normal">Chunked Mode</span>
                <Switch checked={useChunkedMode} onChange={setUseChunkedMode} checkedChildren="ON" unCheckedChildren="OFF" />
              </div>
            </Tooltip>
            {useChunkedMode && isInitialized && (
              <div className="text-sm font-normal text-gray-500">
                <ThunderboltOutlined className="mr-1" />
                {totalChunks} chunks × {chunkDuration}s
              </div>
            )}
          </div>
        </div>
      }
      className="h-full"
      bodyStyle={{ height: "calc(100% - 57px)", padding: 0 }}
    >
      <div className="h-full flex flex-col">
        {/* Progress Bar */}
        <div className="px-4 py-2 border-b">
          <Progress
            percent={progress}
            status={progress === 100 ? "success" : "active"}
            strokeColor={{
              "0%": "#108ee9",
              "100%": "#87d068",
            }}
          />
          <div className="flex justify-between mt-1 text-xs text-gray-500">
            <span>
              {chunkResults.length} / {totalChunks || "?"} chunks processed
            </span>
            <span className="flex items-center gap-2">
              <ThunderboltOutlined />
              {hfoEvents.length} HFOs detected
              {progress < 100 && " (live)"}
            </span>
          </div>
        </div>

        {/* Controls */}
        <div className="px-4 py-3 border-b bg-gray-50">
          <Space size="middle" wrap>
            <Select
              value={selectedChannels}
              mode="multiple"
              placeholder="All channels"
              style={{ minWidth: 200 }}
              onChange={setSelectedChannels}
              options={channels.map((ch) => ({ label: ch, value: ch }))}
              maxTagCount="responsive"
            />

            <Space.Compact>
              <Button onClick={() => handleTimeWindowChange("prev")}>←</Button>
              <Select
                value={timeWindowSize}
                style={{ width: 120 }}
                onChange={handleTimeWindowSizeChange}
                options={[
                  { label: "5 seconds", value: 5 },
                  { label: "10 seconds", value: 10 },
                  { label: "20 seconds", value: 20 },
                  { label: "30 seconds", value: 30 },
                ]}
              />
              <Button onClick={() => handleTimeWindowChange("next")}>→</Button>
            </Space.Compact>

            <span className="text-sm text-gray-600">
              {timeWindow[0].toFixed(1)}s - {timeWindow[1].toFixed(1)}s
              {visibleHFOs.length > 0 && <span className="ml-2 text-blue-600">({visibleHFOs.length} HFOs in view)</span>}
            </span>

            <Button icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />} onClick={toggleFullscreen}>
              {isFullscreen ? "Exit Fullscreen" : "Fullscreen"}
            </Button>
          </Space>
        </div>

        {/* Channel Grid */}
        <div ref={channelViewRef} className="flex-1 overflow-auto p-4">
          {channels.length > 0 ? (
            <ChannelGrid
              channels={visibleChannels}
              channelData={allChannelData}
              timeWindow={timeWindow}
              hfoEvents={visibleHFOs}
              samplingRate={2000} // Default, should be from data
            />
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-48 mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-32"></div>
                </div>
                <p className="text-gray-500 mt-4">{progress > 0 ? "Processing data..." : "Waiting for data..."}</p>
              </div>
            </div>
          )}
        </div>

        {/* Status Bar */}
        <div className="px-4 py-2 border-t bg-gray-50 text-xs text-gray-600">
          <div className="flex justify-between">
            <span>
              {isConnected ? "● Connected" : "○ Disconnected"}
              {useChunkedMode && " (Chunked Mode)"}
            </span>
            <span>
              Total: {hfoEvents.length} HFOs | Duration: {totalDuration.toFixed(1)}s | Channels: {channels.length}
            </span>
          </div>
        </div>
      </div>
    </Card>
  );
};
