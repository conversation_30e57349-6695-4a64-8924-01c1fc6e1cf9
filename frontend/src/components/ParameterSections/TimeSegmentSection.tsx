"use client";

import React from "react";
import { TimeSegment } from "@/types/eeg";
import { BaseParameterSection } from "./BaseParameterSection";
import { InfoBox } from "@/components/ui/InfoBox";
import { FormInput } from "@/components/ui/FormInput";
import { TIME_SEGMENT_MODES, formatDuration } from "./TimeSegmentModes";

interface TimeSegmentSectionProps {
  parameters: TimeSegment;
  onUpdate: (parameters: TimeSegment) => void;
  errors: string[];
  isExpanded: boolean;
  onToggle: () => void;
  status: 'valid' | 'error';
  fileDuration: number;
}


const TimeSegmentSection: React.FC<TimeSegmentSectionProps> = ({
  parameters,
  onUpdate,
  errors,
  isExpanded,
  onToggle,
  status,
  fileDuration,
}) => {
  const updateMode = (mode: 'entire_file' | 'start_end_times' | 'start_time_duration') => {
    onUpdate({
      ...parameters,
      mode,
      // Clear other fields when switching modes
      startDate: mode === 'entire_file' ? undefined : parameters.startDate,
      startTime: mode === 'entire_file' ? undefined : parameters.startTime,
      endDate: mode === 'start_end_times' ? parameters.endDate : undefined,
      endTime: mode === 'start_end_times' ? parameters.endTime : undefined,
      durationSeconds: mode === 'start_time_duration' ? parameters.durationSeconds : undefined,
    });
  };

  const updateField = (field: keyof TimeSegment, value: string | number | undefined) => {
    onUpdate({
      ...parameters,
      [field]: value,
    });
  };


  return (
    <BaseParameterSection
      title="Time Segment"
      status={status}
      errors={errors}
      isExpanded={isExpanded}
      onToggle={onToggle}
    >
      <div className="mt-4 space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Analysis Mode
              </label>
              <div className="space-y-3">
                {TIME_SEGMENT_MODES.map((mode) => (
                  <label
                    key={mode.value}
                    className={`flex items-start gap-3 p-3 border rounded-lg cursor-pointer transition-colors ${
                      parameters.mode === mode.value
                        ? "border-blue-500 bg-blue-50"
                        : "border-gray-200 hover:border-gray-300"
                    }`}
                  >
                    <input
                      type="radio"
                      name="time-segment-mode"
                      value={mode.value}
                      checked={parameters.mode === mode.value}
                      onChange={() => updateMode(mode.value as 'entire_file' | 'start_end_times' | 'start_time_duration')}
                      className="mt-1 w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                    />
                    <div className="flex-1">
                      <div className="text-sm font-medium text-gray-900">
                        {mode.label}
                      </div>
                      <div className="text-sm text-gray-600">
                        {mode.description}
                      </div>
                    </div>
                  </label>
                ))}
              </div>
            </div>

            {parameters.mode !== 'entire_file' && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormInput
                    label="Start Date"
                    type="text"
                    placeholder="dd.mm.yy"
                    value={parameters.startDate || ''}
                    onChange={(e) => updateField('startDate', e.target.value)}
                    error={errors.some(error => error.includes('Start date'))}
                    helperText="Format: dd.mm.yy"
                  />
                  <FormInput
                    label="Start Time"
                    type="text"
                    placeholder="HH:MM:SS"
                    value={parameters.startTime || ''}
                    onChange={(e) => updateField('startTime', e.target.value)}
                    error={errors.some(error => error.includes('Start time'))}
                    helperText="Format: HH:MM:SS"
                  />
                </div>

                {parameters.mode === 'start_end_times' && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormInput
                      label="End Date"
                      type="text"
                      placeholder="dd.mm.yy"
                      value={parameters.endDate || ''}
                      onChange={(e) => updateField('endDate', e.target.value)}
                      error={errors.some(error => error.includes('End date'))}
                      helperText="Format: dd.mm.yy"
                    />
                    <FormInput
                      label="End Time"
                      type="text"
                      placeholder="HH:MM:SS"
                      value={parameters.endTime || ''}
                      onChange={(e) => updateField('endTime', e.target.value)}
                      error={errors.some(error => error.includes('End time'))}
                      helperText="Format: HH:MM:SS"
                    />
                  </div>
                )}

                {parameters.mode === 'start_time_duration' && (
                  <FormInput
                    label="Duration (seconds)"
                    type="number"
                    min="1"
                    max={fileDuration}
                    value={parameters.durationSeconds || ''}
                    onChange={(e) => updateField('durationSeconds', Number(e.target.value))}
                    error={errors.some(error => error.includes('Duration'))}
                    helperText={`Maximum: ${fileDuration}s (${formatDuration(fileDuration)})`}
                  />
                )}
              </div>
            )}

            <div className="bg-gray-50 rounded-lg p-3">
              <h4 className="text-sm font-medium text-gray-900 mb-2">File Information</h4>
              <div className="text-sm text-gray-600">
                <p>Total Duration: {formatDuration(fileDuration)}</p>
                <p>Selected: {parameters.mode === 'entire_file' ? 'Entire file' : 'Custom segment'}</p>
              </div>
            </div>
      </div>

      <InfoBox type="info" className="mt-4">
        <strong>Time Segment Selection:</strong> You can analyze the entire file or focus on specific time periods. 
        Custom time segments are useful for analyzing specific sleep stages or periods of interest.
      </InfoBox>
    </BaseParameterSection>
  );
};

export default TimeSegmentSection;
