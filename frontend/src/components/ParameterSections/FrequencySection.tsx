"use client";

import React from "react";
import { Select, Space, Typography, Al<PERSON>, Row, Col, Card, Tag, Statistic } from "antd";
import { InfoCircleOutlined, ThunderboltOutlined } from "@ant-design/icons";
import { FrequencyFilter, FileInfo } from "@/types/eeg";
import ErrorAlert from "@/components/common/ErrorAlert";

const { Text } = Typography;

interface FrequencySectionProps {
  parameters: FrequencyFilter;
  onUpdate: (parameters: FrequencyFilter) => void;
  errors: string[];
  isExpanded: boolean;
  onToggle: () => void;
  status: "valid" | "error";
  fileInfo: FileInfo;
  validationWarnings?: string[];
}

const FREQUENCY_OPTIONS = {
  lowCutoff: [1, 4, 8, 14, 30, 50, 70, 80, 120, 200, 250],
  highCutoff: [4, 8, 14, 30, 50, 70, 80, 120, 160, 200, 300, 330, 600, 660],
};

const PRESET_BANDS = [
  { name: "Ripple", low: 80, high: 250, description: "Standard ripple band" },
  { name: "Fast Ripple", low: 250, high: 500, description: "Fast ripple oscillations" },
  { name: "Wide Band", low: 80, high: 500, description: "Combined ripple and fast ripple" },
  { name: "Custom", low: null, high: null, description: "Custom frequency range" },
];

const FrequencySection: React.FC<FrequencySectionProps> = ({ parameters, onUpdate, errors, fileInfo, validationWarnings }) => {
  const updateLowCutoff = (value: number) => {
    onUpdate({
      ...parameters,
      lowCutoff: value,
    });
  };

  const updateHighCutoff = (value: number) => {
    onUpdate({
      ...parameters,
      highCutoff: value,
    });
  };

  const applyPreset = (low: number, high: number) => {
    onUpdate({
      lowCutoff: low,
      highCutoff: high,
    });
  };

  const samplingRate = fileInfo.samplingRate;
  const nyquistFreq = samplingRate / 2;
  const maxUsableFreq = samplingRate / 3;

  // Filter high cutoff options based on sampling rate
  const validHighCutoffOptions = FREQUENCY_OPTIONS.highCutoff.filter((freq) => freq <= nyquistFreq);
  const validLowCutoffOptions = FREQUENCY_OPTIONS.lowCutoff.filter((freq) => freq < parameters.highCutoff);

  return (
    <Space direction="vertical" size="large" className="w-full">
      {/* Error Display */}
      <ErrorAlert errors={errors} />

      {/* Sampling Rate Warnings */}
      {validationWarnings && validationWarnings.some(w => w.toLowerCase().includes('sampling rate')) && (
        <Alert
          message="Sampling Rate Notice"
          description={
            <ul className="list-disc list-inside space-y-1">
              {validationWarnings
                .filter(w => w.toLowerCase().includes('sampling rate'))
                .map((warning, index) => (
                  <li key={index} className="text-sm">
                    {warning}
                  </li>
                ))}
            </ul>
          }
          type="warning"
          showIcon
        />
      )}

      {/* Sampling Rate Information */}
      <Row gutter={16}>
        <Col span={8}>
          <Card size="small" className="bg-blue-50">
            <Statistic title="Sampling Rate" value={samplingRate} suffix="Hz" prefix={<ThunderboltOutlined />} />
          </Card>
        </Col>
        <Col span={8}>
          <Card size="small" className="bg-green-50">
            <Statistic
              title="Nyquist Frequency"
              value={nyquistFreq}
              suffix="Hz"
              valueStyle={{ color: nyquistFreq < parameters.highCutoff ? "#ff4d4f" : "#52c41a" }}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card size="small" className="bg-yellow-50">
            <Statistic title="Max Usable Freq" value={Math.round(maxUsableFreq)} suffix="Hz" />
          </Card>
        </Col>
      </Row>

      {/* Preset Bands */}
      <Card size="small">
        <Space direction="vertical" size="small" className="w-full">
          <Text strong>Preset Frequency Bands</Text>
          <div className="flex flex-wrap gap-2">
            {PRESET_BANDS.filter((band) => band.low !== null).map((band) => {
              const isValid = band.high! <= nyquistFreq;
              const isActive = band.low === parameters.lowCutoff && band.high === parameters.highCutoff;

              return (
                <Tag
                  key={band.name}
                  color={isActive ? "black" : isValid ? "default" : "red"}
                  className={`cursor-pointer px-3 py-1 ${isActive ? "border-2 border-black" : ""}`}
                  onClick={() => isValid && applyPreset(band.low!, band.high!)}
                >
                  <Space size="small">
                    <Text strong={isActive}>{band.name}</Text>
                    <Text type="secondary" className="text-xs">
                      ({band.low}-{band.high} Hz)
                    </Text>
                  </Space>
                </Tag>
              );
            })}
          </div>
        </Space>
      </Card>

      {/* Frequency Selectors */}
      <Row gutter={16}>
        <Col span={12}>
          <Card size="small">
            <Space direction="vertical" className="w-full">
              <div className="flex items-center justify-between">
                <Text strong>Low Cutoff Frequency</Text>
                <Tag color="blue">{parameters.lowCutoff} Hz</Tag>
              </div>
              <Select
                value={parameters.lowCutoff}
                onChange={updateLowCutoff}
                options={validLowCutoffOptions.map((freq) => ({
                  label: `${freq} Hz`,
                  value: freq,
                }))}
                className="w-full"
                size="large"
                showSearch
                filterOption={(input, option) => (option?.label ?? "").toLowerCase().includes(input.toLowerCase())}
              />
              <Text type="secondary" className="text-xs">
                High-pass filter to remove low-frequency components
              </Text>
            </Space>
          </Card>
        </Col>

        <Col span={12}>
          <Card size="small">
            <Space direction="vertical" className="w-full">
              <div className="flex items-center justify-between">
                <Text strong>High Cutoff Frequency</Text>
                <Tag color="blue">{parameters.highCutoff} Hz</Tag>
              </div>
              <Select
                value={parameters.highCutoff}
                onChange={updateHighCutoff}
                options={validHighCutoffOptions.map((freq) => ({
                  label: `${freq} Hz`,
                  value: freq,
                  disabled: freq <= parameters.lowCutoff,
                }))}
                className="w-full"
                size="large"
                showSearch
                filterOption={(input, option) => (option?.label ?? "").toLowerCase().includes(input.toLowerCase())}
              />
              <Text type="secondary" className="text-xs">
                Low-pass filter to remove high-frequency components
              </Text>
            </Space>
          </Card>
        </Col>
      </Row>

      {/* Bandwidth Display */}
      <Card size="small" className="bg-gray-50">
        <Row align="middle">
          <Col span={12}>
            <Space>
              <Text strong>Current Bandwidth:</Text>
              <Tag color="black" className="text-base">
                {parameters.lowCutoff} - {parameters.highCutoff} Hz
              </Tag>
            </Space>
          </Col>
          <Col span={12}>
            <Space>
              <Text strong>Bandwidth Width:</Text>
              <Tag color="green" className="text-base">
                {parameters.highCutoff - parameters.lowCutoff} Hz
              </Tag>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Information Box */}
      <Alert
        message="About Frequency Filtering"
        description={
          <Space direction="vertical" size="small">
            <Text>Frequency filtering isolates the signal components relevant to HFO detection:</Text>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>
                <Text strong>Ripples (80-250 Hz)</Text>: Most common HFO type in epilepsy
              </li>
              <li>
                <Text strong>Fast Ripples (250-500 Hz)</Text>: Associated with epileptogenic zones
              </li>
              <li>
                <Text strong>Nyquist Limit</Text>: High cutoff must be ≤ {nyquistFreq} Hz (sampling rate/2)
              </li>
              <li>
                <Text strong>Recommended</Text>: High cutoff ≤ {Math.round(maxUsableFreq)} Hz (sampling rate/3)
              </li>
            </ul>
            {parameters.highCutoff > maxUsableFreq && (
              <Alert
                message="Performance Warning"
                description={`High cutoff (${parameters.highCutoff} Hz) exceeds recommended limit (${Math.round(
                  maxUsableFreq
                )} Hz). This may lead to aliasing artifacts.`}
                type="warning"
                showIcon
                className="mt-2"
              />
            )}
          </Space>
        }
        type="info"
        showIcon
        icon={<InfoCircleOutlined />}
      />
    </Space>
  );
};

export default FrequencySection;
