import React from "react";
import { Row, Col, Card, Statistic, Tag, Space, Typography } from "antd";
import { ThunderboltOutlined } from "@ant-design/icons";

const { Text } = Typography;

interface SamplingRateInfoProps {
  samplingRate: number;
  nyquistFreq: number;
  maxUsableFreq: number;
  highCutoff: number;
}

export const SamplingRateInfo: React.FC<SamplingRateInfoProps> = ({
  samplingRate,
  nyquistFreq,
  maxUsableFreq,
  highCutoff,
}) => (
  <Row gutter={16}>
    <Col span={8}>
      <Card size="small" className="bg-blue-50">
        <Statistic title="Sampling Rate" value={samplingRate} suffix="Hz" prefix={<ThunderboltOutlined />} />
      </Card>
    </Col>
    <Col span={8}>
      <Card size="small" className="bg-green-50">
        <Statistic
          title="Nyquist Frequency"
          value={nyquistFreq}
          suffix="Hz"
          valueStyle={{ color: nyquistFreq < highCutoff ? "#ff4d4f" : "#52c41a" }}
        />
      </Card>
    </Col>
    <Col span={8}>
      <Card size="small" className="bg-yellow-50">
        <Statistic title="Max Usable Freq" value={Math.round(maxUsableFreq)} suffix="Hz" />
      </Card>
    </Col>
  </Row>
);

interface PresetBand {
  name: string;
  low: number | null;
  high: number | null;
  description: string;
}

interface PresetBandsProps {
  presets: PresetBand[];
  currentLow: number;
  currentHigh: number;
  nyquistFreq: number;
  onApplyPreset: (low: number, high: number) => void;
}

export const PresetBands: React.FC<PresetBandsProps> = ({
  presets,
  currentLow,
  currentHigh,
  nyquistFreq,
  onApplyPreset,
}) => (
  <Card size="small">
    <Space direction="vertical" size="small" className="w-full">
      <Text strong>Preset Frequency Bands</Text>
      <div className="flex flex-wrap gap-2">
        {presets.filter((band) => band.low !== null).map((band) => {
          const isValid = band.high! <= nyquistFreq;
          const isActive = band.low === currentLow && band.high === currentHigh;

          return (
            <Tag
              key={band.name}
              color={isActive ? "black" : isValid ? "default" : "red"}
              className={`cursor-pointer px-3 py-1 ${isActive ? "border-2 border-black" : ""}`}
              onClick={() => isValid && onApplyPreset(band.low!, band.high!)}
            >
              <Space size="small">
                <Text strong={isActive}>{band.name}</Text>
                <Text type="secondary" className="text-xs">
                  ({band.low}-{band.high} Hz)
                </Text>
              </Space>
            </Tag>
          );
        })}
      </div>
    </Space>
  </Card>
);

interface BandwidthDisplayProps {
  lowCutoff: number;
  highCutoff: number;
}

export const BandwidthDisplay: React.FC<BandwidthDisplayProps> = ({ lowCutoff, highCutoff }) => (
  <Card size="small" className="bg-gray-50">
    <Row align="middle">
      <Col span={12}>
        <Space>
          <Text strong>Current Bandwidth:</Text>
          <Tag color="black" className="text-base">
            {lowCutoff} - {highCutoff} Hz
          </Tag>
        </Space>
      </Col>
      <Col span={12}>
        <Space>
          <Text strong>Bandwidth Width:</Text>
          <Tag color="green" className="text-base">
            {highCutoff - lowCutoff} Hz
          </Tag>
        </Space>
      </Col>
    </Row>
  </Card>
);