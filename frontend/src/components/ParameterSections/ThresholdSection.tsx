"use client";

import React from "react";
import { <PERSON>lide<PERSON>, <PERSON>lt<PERSON>, Typography, <PERSON>, Row, Col, Card, Alert } from "antd";
import { InfoCircleOutlined } from "@ant-design/icons";
import { ThresholdParameters } from "@/types/eeg";
import ErrorAlert from "@/components/common/ErrorAlert";

const { Text } = Typography;

interface ThresholdSectionProps {
  parameters: ThresholdParameters;
  onUpdate: (parameters: ThresholdParameters) => void;
  errors: string[];
  isExpanded: boolean;
  onToggle: () => void;
  status: 'valid' | 'error' | 'success';
}

const THRESHOLD_CONFIGS = {
  amplitude1: { 
    min: 2, 
    max: 5, 
    step: 1, 
    label: "Amplitude 1", 
    description: "HFO amp >= energy signal by (times of std)",
    unit: "× std"
  },
  amplitude2: { 
    min: 2, 
    max: 5, 
    step: 1, 
    label: "Amplitude 2", 
    description: "HFO amp >= mean baseline signal by (times of std)",
    unit: "× std"
  },
  peaks1: { 
    min: 2, 
    max: 8, 
    step: 1, 
    label: "Peaks 1", 
    description: "Number of peaks in HFO >= Amplitude 1",
    unit: "peaks"
  },
  peaks2: { 
    min: 2, 
    max: 6, 
    step: 1, 
    label: "Peaks 2", 
    description: "Number of peaks in HFO >= Amplitude 2",
    unit: "peaks"
  },
  duration: { 
    min: 5, 
    max: 15, 
    step: 1, 
    label: "Duration", 
    description: "HFO length >= (ms)",
    unit: "ms"
  },
  temporalSync: { 
    min: 5, 
    max: 12, 
    step: 1, 
    label: "Temporal Sync", 
    description: "Inter HFO interval in any channel <= (ms)",
    unit: "ms"
  },
  spatialSync: { 
    min: 5, 
    max: 12, 
    step: 1, 
    label: "Spatial Sync", 
    description: "Inter HFO interval across channels <= (ms)",
    unit: "ms"
  },
};

const ThresholdSection: React.FC<ThresholdSectionProps> = ({
  parameters,
  onUpdate,
  errors,
}) => {
  const updateParameter = (key: keyof ThresholdParameters, value: number) => {
    onUpdate({
      ...parameters,
      [key]: value,
    });
  };

  const getMarks = (min: number, max: number) => {
    const marks: Record<number, string> = {};
    marks[min] = `${min}`;
    marks[max] = `${max}`;
    return marks;
  };

  return (
    <Space direction="vertical" size="large" className="w-full">
      {/* Error Display */}
      <ErrorAlert errors={errors} />

      {/* Parameter Sliders */}
      <Row gutter={[24, 24]}>
        {Object.entries(THRESHOLD_CONFIGS).map(([key, config]) => {
          const value = parameters[key as keyof ThresholdParameters];
          const isInvalid = errors.some(error => error.toLowerCase().includes(key.toLowerCase()));

          return (
            <Col span={12} key={key}>
              <Card 
                size="small"
                className={isInvalid ? "border-red-300" : "border-gray-200"}
                styles={{
                  body: { padding: "16px" }
                }}
              >
                <Space direction="vertical" size="small" className="w-full">
                  <div className="flex items-center justify-between">
                    <Space>
                      <Text strong>{config.label}</Text>
                      <Tooltip title={config.description}>
                        <InfoCircleOutlined className="text-gray-400 cursor-help" />
                      </Tooltip>
                    </Space>
                    <Text code className="text-base">
                      {value} {config.unit}
                    </Text>
                  </div>

                  <Slider
                    min={config.min}
                    max={config.max}
                    step={config.step}
                    value={value}
                    onChange={(val) => updateParameter(key as keyof ThresholdParameters, val)}
                    marks={getMarks(config.min, config.max)}
                    tooltip={{
                      formatter: (val) => `${val} ${config.unit}`
                    }}
                    className="mb-4"
                  />

                  <Text type="secondary" className="text-xs">
                    {config.description}
                  </Text>
                </Space>
              </Card>
            </Col>
          );
        })}
      </Row>

      {/* Information Box */}
      <Alert
        message="About Threshold Parameters"
        description={
          <Space direction="vertical" size="small">
            <Text>These settings control the sensitivity of HFO detection:</Text>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>
                <Text strong>Amplitude parameters</Text> determine the minimum signal strength for HFO detection
              </li>
              <li>
                <Text strong>Peak counts</Text> ensure detected events have sufficient oscillatory content
              </li>
              <li>
                <Text strong>Duration</Text> sets the minimum length for valid HFO events
              </li>
              <li>
                <Text strong>Synchronization</Text> parameters control temporal and spatial grouping of HFOs
              </li>
            </ul>
            <Text type="secondary" className="text-xs">
              Higher thresholds reduce false positives but may miss weaker HFOs. Adjust based on your signal quality and analysis requirements.
            </Text>
          </Space>
        }
        type="info"
        showIcon
        icon={<InfoCircleOutlined />}
      />
    </Space>
  );
};

export default ThresholdSection;