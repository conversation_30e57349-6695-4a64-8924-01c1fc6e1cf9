import { AnalysisParameters, FileInfo, ValidationErrors, DEFAULT_PARAMETERS } from "@/types/eeg";

/**
 * Get the validation status for a section
 */
export const getSectionStatus = (
  validationErrors: ValidationErrors,
  sectionKey: keyof ValidationErrors
): "valid" | "error" => {
  return validationErrors[sectionKey] ? "error" : "valid";
};

/**
 * Create enhanced default parameters based on file info
 */
export const createEnhancedDefaults = (fileInfo: FileInfo): AnalysisParameters => {
  return {
    ...DEFAULT_PARAMETERS,
    channelSelection: {
      ...DEFAULT_PARAMETERS.channelSelection,
      selectedLeads: fileInfo.channels?.slice(0, Math.min(10, fileInfo.channels.length)) || [],
    },
  };
};

/**
 * Check if bipolar montage has enough channels
 */
export const validateBipolarChannels = (
  selectedLeads: string[],
  availableChannels: string[]
): boolean => {
  const channelCount = selectedLeads.length > 0 
    ? selectedLeads.length 
    : availableChannels.length;
  return channelCount >= 2;
};