export const FREQUENCY_OPTIONS = {
  lowCutoff: [1, 4, 8, 14, 30, 50, 70, 80, 120, 200, 250],
  highCutoff: [4, 8, 14, 30, 50, 70, 80, 120, 160, 200, 300, 330, 600, 660],
};

export const PRESET_BANDS = [
  { name: "Ripple", low: 80, high: 250, description: "Standard ripple band" },
  { name: "Fast Ripple", low: 250, high: 500, description: "Fast ripple oscillations" },
  { name: "Wide Band", low: 80, high: 500, description: "Combined ripple and fast ripple" },
  { name: "Custom", low: null, high: null, description: "Custom frequency range" },
];

export const calculateNyquistFrequency = (samplingRate: number): number => {
  return samplingRate / 2;
};

export const calculateMaxUsableFrequency = (samplingRate: number): number => {
  return samplingRate / 3;
};

export const isFrequencyValid = (frequency: number, nyquistFreq: number): boolean => {
  return frequency <= nyquistFreq;
};