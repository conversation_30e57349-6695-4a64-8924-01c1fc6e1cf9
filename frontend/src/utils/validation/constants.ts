export const THRESHOLD_CONSTRAINTS = {
  amplitude1: { min: 2, max: 5 },
  amplitude2: { min: 2, max: 5 },
  peaks1: { min: 2, max: 8 },
  peaks2: { min: 2, max: 6 },
  duration: { min: 5, max: 15 },
  temporalSync: { min: 5, max: 12 },
  spatialSync: { min: 5, max: 12 },
};

export const FREQUENCY_OPTIONS = {
  lowCutoff: [1, 4, 8, 14, 30, 50, 70, 80, 120, 200, 250],
  highCutoff: [4, 8, 14, 30, 50, 70, 80, 120, 160, 200, 300, 330, 600, 660],
};

export const DATE_REGEX = /^\d{2}\.\d{2}\.\d{2}$/;
export const TIME_REGEX = /^\d{2}:\d{2}:\d{2}$/;