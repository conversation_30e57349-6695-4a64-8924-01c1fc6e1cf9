import { AnalysisParameters, FileInfo, ValidationErrors } from '@/types/eeg';
import {
  validateThresholds,
  validateFrequency,
  validateMontage,
  validateTimeSegment,
  validateChannelSelection
} from './validators';

export * from './validators';
export * from './constants';

export function validateAllParameters(
  parameters: AnalysisParameters,
  fileInfo?: FileInfo
): ValidationErrors {
  const errors: ValidationErrors = {};

  const thresholdErrors = validateThresholds(parameters.thresholds);
  if (thresholdErrors.length > 0) {
    errors.thresholds = thresholdErrors;
  }

  const frequencyErrors = validateFrequency(parameters.frequency, fileInfo?.samplingRate);
  if (frequencyErrors.length > 0) {
    errors.frequency = frequencyErrors;
  }

  const montageErrors = validateMontage(parameters.montage, fileInfo?.channels);
  if (montageErrors.length > 0) {
    errors.montage = montageErrors;
  }

  const timeSegmentErrors = validateTimeSegment(parameters.timeSegment, fileInfo?.durationSeconds);
  if (timeSegmentErrors.length > 0) {
    errors.timeSegment = timeSegmentErrors;
  }

  const channelErrors = validateChannelSelection(parameters.channelSelection, fileInfo?.channels);
  if (channelErrors.length > 0) {
    errors.channelSelection = channelErrors;
  }

  // Cross-validation: Check if bipolar montage has enough selected channels
  if (parameters.montage.type === 'bipolar' && parameters.channelSelection.selectedLeads.length > 0) {
    if (parameters.channelSelection.selectedLeads.length < 2) {
      if (!errors.montage) {
        errors.montage = [];
      }
      errors.montage.push('Bipolar montage requires at least 2 channels to be selected');
    }
  }

  return errors;
}

export function areParametersValid(errors: ValidationErrors): boolean {
  return Object.keys(errors).length === 0;
}