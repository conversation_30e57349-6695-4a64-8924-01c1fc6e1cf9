import { AnalysisParameters, FileInfo } from "@/types/eeg";
import { toBackendFormat, fromBackendFormat } from "./transformers";

const API_BASE_URL = "http://localhost:8000";

export class APIError extends Error {
  constructor(message: string, public status?: number, public errors?: string[]) {
    super(message);
    this.name = "APIError";
  }
}

/**
 * Get file information without starting analysis
 */
export async function getFileInfo(filepath: string): Promise<FileInfo> {
  try {
    // Use the analyze endpoint to get file info
    const response = await fetch(`${API_BASE_URL}/api/analyze`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        filepath,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));

      // Extract the most specific error message available
      let errorMessage = "Failed to load file information";

      if (errorData.details && Array.isArray(errorData.details) && errorData.details.length > 0) {
        errorMessage = errorData.details[0];
      } else if (errorData.message) {
        errorMessage = errorData.message;
      } else if (errorData.detail) {
        errorMessage = errorData.detail;
      }

      // Clean up common backend error prefixes
      errorMessage = errorMessage.replace("ValidationError: ", "").replace("EDF file validation failed: ", "").replace("ValueError: ", "");

      throw new APIError(errorMessage, response.status, errorData.errors || errorData.details);
    }

    const data = await response.json();

    // Extract file info from the analyze response
    const fileInfo = data.file_info || {};

    // Ensure we have all required fields with proper fallbacks
    const completeFileInfo = {
      file_id: fileInfo.file_id || filepath,
      filename: fileInfo.filename || filepath.split("/").pop() || filepath.split("\\").pop() || "Unknown",
      start_date: fileInfo.start_date || "",
      start_time: fileInfo.start_time || "",
      end_date: fileInfo.end_date || "",
      end_time: fileInfo.end_time || "",
      sampling_rate: fileInfo.sampling_rate || 256,
      channels: fileInfo.channels || [],
      duration_seconds: fileInfo.duration_seconds || 0,
    };

    // Transform file info to camelCase
    const transformedFileInfo = fromBackendFormat(completeFileInfo) as unknown as FileInfo;

    // Add validation warnings if present
    if (data.validation_warnings) {
      transformedFileInfo.validationWarnings = data.validation_warnings;
    }

    // Add status message if present
    if (data.message) {
      transformedFileInfo.statusMessage = data.message;
    }

    return transformedFileInfo;
  } catch (error) {
    // Handle network errors
    if (error instanceof TypeError && error.message === "Failed to fetch") {
      throw new Error("Cannot connect to server. Please ensure the backend is running on port 8000.");
    }

    // Re-throw if it's already an APIError
    if (error instanceof APIError) {
      throw error;
    }

    // Handle other errors
    throw new Error(error instanceof Error ? error.message : "An unexpected error occurred");
  }
}

/**
 * Start analysis with parameters
 */
export async function startAnalysis(filepath: string, parameters: AnalysisParameters): Promise<void> {
  const response = await fetch(`${API_BASE_URL}/api/analyze/start`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      filepath,
      parameters: toBackendFormat(parameters),
    }),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new APIError(errorData.message || errorData.detail || "Failed to start analysis", response.status, errorData.errors);
  }
}
