import { AnalysisParameters } from "@/types/eeg";

// Transform frontend camelCase to backend snake_case
export function toBackendFormat(params: AnalysisParameters): Record<string, unknown> {
  return {
    thresholds: {
      amplitude1: params.thresholds.amplitude1,
      amplitude2: params.thresholds.amplitude2,
      peaks1: params.thresholds.peaks1,
      peaks2: params.thresholds.peaks2,
      duration: params.thresholds.duration,
      temporal_sync: params.thresholds.temporalSync,
      spatial_sync: params.thresholds.spatialSync,
    },
    montage: {
      type: params.montage.type,
      reference_channel: params.montage.referenceChannel,
    },
    frequency: {
      low_cutoff: params.frequency.lowCutoff,
      high_cutoff: params.frequency.highCutoff,
    },
    time_segment: {
      mode: params.timeSegment.mode,
      start_date: params.timeSegment.startDate,
      start_time: params.timeSegment.startTime,
      end_date: params.timeSegment.endDate,
      end_time: params.timeSegment.endTime,
      duration_seconds: params.timeSegment.durationSeconds,
    },
    channel_selection: {
      selected_leads: params.channelSelection.selectedLeads,
      contact_specifications: params.channelSelection.contactSpecifications,
    },
  };
}

// Transform backend snake_case to frontend camelCase
export function fromBackendFormat(data: Record<string, unknown>): Record<string, unknown> {
  if (!data) return data;

  // Transform FileInfo - check for file_id or channels (both are indicators of FileInfo)
  if (data.file_id !== undefined || data.channels !== undefined) {
    return {
      fileId: data.file_id || "",
      filename: data.filename || "",
      startDate: data.start_date || "",
      startTime: data.start_time || "",
      endDate: data.end_date || "",
      endTime: data.end_time || "",
      samplingRate: data.sampling_rate || 0,
      channels: data.channels || [],
      durationSeconds: data.duration_seconds || 0,
    };
  }

  // Transform ChunkResult
  if (data.chunk_number !== undefined) {
    return {
      type: data.type,
      timeRange: data.time_range,
      hfoEvents: (data.hfo_events as Record<string, unknown>[])?.map((hfo) => ({
        type: hfo.type,
        channel: hfo.channel,
        startTime: hfo.start_time,
        endTime: hfo.end_time,
        amplitude: hfo.amplitude,
        frequency: hfo.frequency,
        durationMs: hfo.duration_ms,
        peakFrequency: hfo.peak_frequency,
        power: hfo.power,
        sampleStart: hfo.sample_start,
        sampleEnd: hfo.sample_end,
      })),
      channelData: data.channel_data,
      progress: data.progress,
      chunkNumber: data.chunk_number,
      totalChunks: data.total_chunks,
    };
  }

  return data;
}