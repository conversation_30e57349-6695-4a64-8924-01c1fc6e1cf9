"use client";

import { useState } from "react";
import { Row, Col, Card, Switch, Space, Typography } from "antd";
import { FileUploadCard } from "@/components/FileUploadCard";
import { ParameterSettingsCard } from "@/components/ParameterSettingsCard";
import { ChunkedEEGViewer } from "@/components/EEGViewer/ChunkedEEGViewer";
import { StepNavigation } from "@/components/StepNavigation";
import { ConnectionStatus } from "@/components/ConnectionStatus";
import { ChunkedWebSocketProvider, useChunkedWebSocket } from "@/contexts/ChunkedWebSocketContext";
import { WebSocketProvider, useWebSocket } from "@/contexts/WebSocketContext";
import { AppLayout } from "@/components/layout/AppLayout";
import { useAppWorkflow } from "@/hooks/useAppWorkflow";
import { AnalysisParameters } from "@/types/app";

const { Title, Text } = Typography;

function AppContent() {
  const [useChunkedMode, setUseChunkedMode] = useState(true);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [parameters, setParameters] = useState<AnalysisParameters>({
    thresholds: {
      amplitude1: 2,
      amplitude2: 2,
      peaks1: 6,
      peaks2: 3,
      duration: 10,
      temporalSync: 10,
      spatialSync: 10,
    },
    montage: {
      type: "bipolar",
      referenceChannel: undefined,
    },
    frequency: {
      lowCutoff: 50,
      highCutoff: 300,
    },
    timeSegment: {
      mode: "entire_file",
      startTime: undefined,
      endTime: undefined,
      duration: undefined,
    },
    channelSelection: {
      selectedLeads: [],
      contactSpecifications: {},
    },
  });

  const { currentStep, setCurrentStep } = useAppWorkflow();

  return (
    <AppLayout>
      <div className="p-6">
        {/* Header with Mode Switch */}
        <div className="mb-6">
          <Row align="middle" justify="space-between">
            <Col>
              <Title level={3} className="mb-0">
                HFO Analysis Pipeline
              </Title>
            </Col>
            <Col>
              <Space align="center">
                <Text>Processing Mode:</Text>
                <Switch
                  checked={useChunkedMode}
                  onChange={setUseChunkedMode}
                  checkedChildren="Chunked"
                  unCheckedChildren="Regular"
                  style={{ minWidth: 80 }}
                />
                <ConnectionStatus />
              </Space>
            </Col>
          </Row>
          {useChunkedMode && (
            <Card className="mt-4 bg-blue-50 border-blue-200">
              <Space direction="vertical" size="small">
                <Text strong className="text-blue-700">
                  Chunked Processing Mode Enabled
                </Text>
                <Text className="text-blue-600">
                  • Files are processed in memory-efficient chunks
                  <br />
                  • HFO detection happens progressively as data loads
                  <br />• Results stream in real-time for better performance
                </Text>
              </Space>
            </Card>
          )}
        </div>

        {/* Step Navigation */}
        <StepNavigation currentStep={currentStep} />

        {/* Main Content */}
        <Row gutter={[16, 16]} className="mt-6">
          {currentStep === 0 && (
            <>
              <Col span={12}>
                <FileUploadCard onFileSelect={setUploadedFile} selectedFile={uploadedFile} />
              </Col>
              <Col span={12}>
                <ParameterSettingsCard
                  parameters={parameters}
                  onParametersChange={setParameters}
                  onNext={() => setCurrentStep(1)}
                  uploadedFile={uploadedFile}
                />
              </Col>
            </>
          )}

          {currentStep === 1 && (
            <Col span={24}>
              {useChunkedMode ? (
                <ChunkedWebSocketProvider>
                  <ChunkedEEGViewer />
                </ChunkedWebSocketProvider>
              ) : (
                <WebSocketProvider>
                  <div className="text-center p-8">
                    <Text>Regular mode viewer - using existing implementation</Text>
                  </div>
                </WebSocketProvider>
              )}
            </Col>
          )}
        </Row>
      </div>
    </AppLayout>
  );
}

export default function ChunkedModePage() {
  return <AppContent />;
}
