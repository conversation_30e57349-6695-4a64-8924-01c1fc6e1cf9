"use client";

import { useCallback, useState } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { renderStep } from "@/components/steps";
import { useAppWorkflow } from "@/hooks/useAppWorkflow";
import { useAnalysisOrchestration } from "@/hooks/useAnalysisOrchestration";

export default function Home() {
  const [, setIsValid] = useState(false);
  
  const {
    workflowState,
    fileInfo,
    parameters,
    handleFileSelect,
    handleParametersChange,
    transitionToAnalysis,
    resetWorkflow,
    setParameters,
  } = useAppWorkflow();

  const { handleStartAnalysis } = useAnalysisOrchestration({
    filepath: workflowState.filepath,
    parameters,
    fileInfo,
    onSuccess: transitionToAnalysis,
    onParametersUpdate: setParameters,
  });

  const handleValidationChange = useCallback((valid: boolean) => {
    setIsValid(valid);
  }, []);

  const stepProps = {
    fileInfo,
    parameters,
    error: workflowState.error,
    isLoading: workflowState.isLoading,
    onFileSelect: handleFileSelect,
    onParametersChange: handleParametersChange,
    onValidationChange: handleValidationChange,
    onStartAnalysis: handleStartAnalysis,
    onBack: resetWorkflow,
  };

  return (
    <AppLayout>
      {renderStep(workflowState.appState, stepProps)}
    </AppLayout>
  );
}
