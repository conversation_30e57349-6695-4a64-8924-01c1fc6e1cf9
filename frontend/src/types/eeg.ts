export interface ThresholdParameters {
  amplitude1: number;
  amplitude2: number;
  peaks1: number;
  peaks2: number;
  duration: number;
  temporalSync: number;
  spatialSync: number;
}

export interface MontageConfig {
  type: "bipolar" | "average" | "referential";
  referenceChannel?: string;
}

export interface FrequencyFilter {
  lowCutoff: number;
  highCutoff: number;
}

export interface TimeSegment {
  mode: "entire_file" | "start_end_times" | "start_time_duration";
  startDate?: string;
  startTime?: string;
  endDate?: string;
  endTime?: string;
  durationSeconds?: number;
}

export interface ChannelSelection {
  selectedLeads: string[];
  contactSpecifications: Record<string, string>;
}

export interface AnalysisParameters {
  thresholds: ThresholdParameters;
  montage: MontageConfig;
  frequency: FrequencyFilter;
  timeSegment: TimeSegment;
  channelSelection: ChannelSelection;
}

export interface FileInfo {
  fileId: string;
  filename: string;
  startDate: string;
  startTime: string;
  endDate: string;
  endTime: string;
  samplingRate: number;
  channels: string[];
  durationSeconds: number;
  validationWarnings?: string[];
  statusMessage?: string;
}

export interface HFOEvent {
  type: 'accepted' | 'rejected' | 'rejected_long';
  channel: string;
  startTime: number;
  endTime: number;
  amplitude?: number;
  frequency?: number;
  durationMs?: number;
  peakFrequency?: number;
  power?: number;
  sampleStart?: number;
  sampleEnd?: number;
}

export interface ChunkResult {
  type: string;
  timeRange: [number, number];
  hfoEvents: HFOEvent[];
  channelData: Record<string, number[]>;
  progress: number;
  chunkNumber: number;
  totalChunks: number;
}

export interface WebSocketMessage {
  type: "preview_ready" | "chunk_complete" | "hfo_detected" | "analysis_complete" | "error" | "status_update";
  data?: ChunkResult | FileInfo | HFOEvent[] | Record<string, unknown>;
  message?: string;
  status?: string;
  progress?: number;
}

// Validation error structure
export interface ValidationErrors {
  [key: string]: string[];
}

// Default parameters matching backend defaults
export const DEFAULT_PARAMETERS: AnalysisParameters = {
  thresholds: {
    amplitude1: 2,
    amplitude2: 2,
    peaks1: 6,
    peaks2: 3,
    duration: 10,
    temporalSync: 10,
    spatialSync: 10,
  },
  montage: {
    type: "bipolar",
  },
  frequency: {
    lowCutoff: 50,
    highCutoff: 300,
  },
  timeSegment: {
    mode: "entire_file",
  },
  channelSelection: {
    selectedLeads: [],
    contactSpecifications: {},
  },
};
