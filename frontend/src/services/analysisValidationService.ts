import { AnalysisParameters, FileInfo, ValidationErrors, DEFAULT_PARAMETERS } from "@/types/eeg";
import { validateAllParameters, areParametersValid } from "@/utils/validation";
import { ParameterValidationResult } from "@/types/app";

export const applyParameterDefaults = (
  parameters: AnalysisParameters,
  fileInfo: FileInfo
): AnalysisParameters => {
  return {
    ...DEFAULT_PARAMETERS,
    ...parameters,
    channelSelection: {
      ...DEFAULT_PARAMETERS.channelSelection,
      ...parameters.channelSelection,
      selectedLeads:
        parameters.channelSelection.selectedLeads.length > 0
          ? parameters.channelSelection.selectedLeads
          : fileInfo.channels?.slice(0, Math.min(10, fileInfo.channels.length)) || [],
    },
  };
};

export const checkBipolarMontageRequirements = (errors: ValidationErrors): boolean => {
  const errorMessages = Object.values(errors).flat();
  return errorMessages.some(
    (err) => err.toLowerCase().includes("bipolar") && err.toLowerCase().includes("channels")
  );
};

export const validateWithDefaults = (
  parameters: AnalysisParameters,
  fileInfo: FileInfo
): ParameterValidationResult => {
  const finalParameters = applyParameterDefaults(parameters, fileInfo);
  const validationErrors = validateAllParameters(finalParameters, fileInfo);
  const isValid = areParametersValid(validationErrors);
  const hasBipolarError = checkBipolarMontageRequirements(validationErrors);
  const errors = Object.values(validationErrors).flat();

  return {
    isValid,
    errors,
    hasBipolarError,
  };
};

export const prepareAnalysisParameters = (
  parameters: AnalysisParameters,
  fileInfo: FileInfo
): { 
  finalParameters: AnalysisParameters; 
  validationResult: ParameterValidationResult;
} => {
  const finalParameters = applyParameterDefaults(parameters, fileInfo);
  const validationErrors = validateAllParameters(finalParameters, fileInfo);
  const isValid = areParametersValid(validationErrors);
  const hasBipolarError = checkBipolarMontageRequirements(validationErrors);
  const errors = Object.values(validationErrors).flat();

  return {
    finalParameters,
    validationResult: {
      isValid,
      errors,
      hasBipolarError,
    },
  };
};

export const getValidationErrorMessage = (validationResult: ParameterValidationResult): string => {
  if (validationResult.hasBipolarError) {
    return "Bipolar montage requires at least 2 channels to be selected. Please select more channels or change the montage type.";
  }
  return `Validation errors: ${validationResult.errors.join(", ")}`;
};

export const getErrorCount = (errors: string[]): number => {
  return errors.length;
};