import { useState, useEffect } from "react";
import { AnalysisParameters, FileInfo, ValidationErrors } from "@/types/eeg";
import { validateAllParameters, areParametersValid } from "@/utils/validation";

interface UseParameterValidationResult {
  validationErrors: ValidationErrors;
  isValid: boolean;
  validateParameters: (parameters: AnalysisParameters, fileInfo: FileInfo) => void;
}

export const useParameterValidation = (
  parameters: AnalysisParameters,
  fileInfo: FileInfo | null
): UseParameterValidationResult => {
  const [validationErrors, setValidationErrors] = useState<ValidationErrors>({});
  const [isValid, setIsValid] = useState(false);

  const validateParameters = (params: AnalysisParameters, info: FileInfo) => {
    const errors = validateAllParameters(params, info);
    setValidationErrors(errors);
    setIsValid(areParametersValid(errors));
  };

  useEffect(() => {
    if (fileInfo) {
      validateParameters(parameters, fileInfo);
    }
  }, [parameters, fileInfo]);

  return {
    validationErrors,
    isValid,
    validateParameters,
  };
};