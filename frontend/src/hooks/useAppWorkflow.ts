import { useState, useCallback } from "react";
import { AppState, AppWorkflowState } from "@/types/app";
import { AnalysisParameters, DEFAULT_PARAMETERS } from "@/types/eeg";
import { useFileAnalysis } from "./useFileAnalysis";

export const useAppWorkflow = () => {
  const [appState, setAppState] = useState<AppState>("file-selection");
  const [filepath, setFilepath] = useState("");
  const [parameters, setParameters] = useState<AnalysisParameters>(DEFAULT_PARAMETERS);
  
  const { fileInfo, isLoading, error, loadFileInfo, clearError } = useFileAnalysis();

  const handleFileSelect = useCallback(async (selectedFilepath: string) => {
    setFilepath(selectedFilepath);
    clearError();

    if (!selectedFilepath) {
      return;
    }

    // Wait for the result and only transition on success
    const success = await loadFileInfo(selectedFilepath);
    
    if (success) {
      setAppState("settings-configuration");
    }
  }, [loadFileInfo, clearError]);

  const handleParametersChange = useCallback((newParameters: AnalysisParameters) => {
    setParameters(newParameters);
  }, []);

  const transitionToAnalysis = useCallback(() => {
    setAppState("analysis");
  }, []);

  const resetWorkflow = useCallback(() => {
    setAppState("file-selection");
    setFilepath("");
    setParameters(DEFAULT_PARAMETERS);
    clearError();
  }, [clearError]);

  const workflowState: AppWorkflowState = {
    appState,
    filepath,
    isLoading,
    error,
  };

  return {
    workflowState,
    fileInfo,
    parameters,
    handleFileSelect,
    handleParametersChange,
    transitionToAnalysis,
    resetWorkflow,
    setParameters,
  };
};