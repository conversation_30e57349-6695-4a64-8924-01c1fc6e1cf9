import { useState, useCallback } from "react";
import { message } from "antd";
import { AnalysisParameters, FileInfo } from "@/types/eeg";
import { startAnalysis } from "@/utils/api";
import { 
  prepareAnalysisParameters, 
  getValidationErrorMessage,
  getErrorCount
} from "@/services/analysisValidationService";

interface UseAnalysisOrchestrationProps {
  filepath: string;
  parameters: AnalysisParameters;
  fileInfo: FileInfo | null;
  onSuccess: () => void;
  onParametersUpdate: (params: AnalysisParameters) => void;
}

export const useAnalysisOrchestration = ({
  filepath,
  parameters,
  fileInfo,
  onSuccess,
  onParametersUpdate,
}: UseAnalysisOrchestrationProps) => {
  const [isStarting, setIsStarting] = useState(false);
  const [analysisError, setAnalysisError] = useState("");

  const handleStartAnalysis = useCallback(async () => {
    if (!filepath || !fileInfo) {
      const errorMsg = "Please select a valid EDF file before starting analysis";
      message.error(errorMsg);
      setAnalysisError(errorMsg);
      return;
    }

    const { finalParameters, validationResult } = prepareAnalysisParameters(parameters, fileInfo);

    if (!validationResult.isValid) {
      const errorCount = getErrorCount(validationResult.errors);
      
      message.warning({
        content: `Found ${errorCount} validation issue${errorCount > 1 ? "s" : ""}. Using default values where possible.`,
        duration: 4,
      });

      onParametersUpdate(finalParameters);

      const { validationResult: newValidationResult } = prepareAnalysisParameters(finalParameters, fileInfo);

      if (!newValidationResult.isValid) {
        const errorMessage = getValidationErrorMessage(newValidationResult);
        
        if (newValidationResult.hasBipolarError) {
          message.error({
            content: `⚠️ ${errorMessage}`,
            duration: 6,
          });
        } else {
          message.error({
            content: `Cannot start analysis: ${newValidationResult.errors.join(", ")}`,
            duration: 6,
          });
        }

        setAnalysisError(errorMessage);
        return;
      }
    }

    message.success({
      content: "Starting EEG analysis with configured parameters...",
      duration: 3,
    });

    try {
      setAnalysisError("");
      setIsStarting(true);
      await startAnalysis(filepath, finalParameters);
      
      message.info({
        content: "Analysis started successfully. Switching to real-time view...",
        duration: 2,
      });
      
      onSuccess();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to start analysis";
      message.error({
        content: `Analysis failed: ${errorMessage}`,
        duration: 6,
      });
      setAnalysisError(errorMessage);
    } finally {
      setIsStarting(false);
    }
  }, [filepath, parameters, fileInfo, onSuccess, onParametersUpdate]);

  return {
    handleStartAnalysis,
    isStarting,
    analysisError,
  };
};