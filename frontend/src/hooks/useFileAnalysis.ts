import { useState, useCallback } from "react";
import { FileInfo } from "@/types/eeg";
import { getFileInfo } from "@/utils/api";

interface UseFileAnalysisResult {
  fileInfo: FileInfo | null;
  isLoading: boolean;
  error: string;
  loadFileInfo: (filepath: string) => Promise<boolean>;
  clearError: () => void;
}

export const useFileAnalysis = (): UseFileAnalysisResult => {
  const [fileInfo, setFileInfo] = useState<FileInfo | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  const loadFileInfo = useCallback(async (filepath: string): Promise<boolean> => {
    if (!filepath) {
      setFileInfo(null);
      return false;
    }

    try {
      setIsLoading(true);
      setError("");
      const info = await getFileInfo(filepath);
      setFileInfo(info);
      return true; // Success
    } catch (err) {
      let errorMessage = "Failed to load file information";

      if (err instanceof Error) {
        // Extract user-friendly error message
        if (err.message.includes("Failed to fetch") || err.message.includes("NetworkError")) {
          errorMessage = "Cannot connect to server. Please ensure the backend is running.";
        } else if (err.message.includes("File not found")) {
          errorMessage = "File not found. Please check the file path.";
        } else if (err.message.includes("not a valid EDF")) {
          errorMessage = "Invalid EDF file format.";
        } else {
          errorMessage = err.message;
        }
      }

      setError(errorMessage);
      setFileInfo(null);
      return false; // Failure
    } finally {
      setIsLoading(false);
    }
  }, []);

  const clearError = useCallback(() => {
    setError("");
  }, []);

  return {
    fileInfo,
    isLoading,
    error,
    loadFileInfo,
    clearError,
  };
};
