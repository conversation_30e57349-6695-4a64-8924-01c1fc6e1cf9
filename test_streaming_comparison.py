#!/usr/bin/env python3
"""
Test script to compare legacy vs true streaming EEG processing performance.
This demonstrates the elimination of the "performance cliff" in the new system.
"""

import asyncio
import websockets
import json
import time
import requests
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class StreamingPerformanceTest:
    """Test client to compare legacy vs streaming performance"""
    
    def __init__(self, server_url="http://localhost:8000", ws_url="ws://localhost:8000"):
        self.server_url = server_url
        self.ws_url = ws_url
        self.test_file = "/Users/<USER>/Downloads/Organized/Work_Projects/Biormika/edf-files/15.edf"
        
    async def upload_test_file(self):
        """Upload test EDF file to server"""
        logger.info("Uploading test EDF file...")
        
        if not Path(self.test_file).exists():
            logger.error(f"Test file not found: {self.test_file}")
            return False
        
        try:
            with open(self.test_file, 'rb') as f:
                files = {'file': f}
                response = requests.post(f"{self.server_url}/api/upload", files=files)
            
            if response.status_code == 200:
                logger.info("✅ File uploaded successfully")
                return True
            else:
                logger.error(f"❌ Upload failed: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Upload error: {e}")
            return False
    
    async def test_legacy_processing(self):
        """Test legacy batch processing system"""
        logger.info("\n" + "="*60)
        logger.info("🔄 TESTING LEGACY BATCH PROCESSING SYSTEM")
        logger.info("="*60)
        
        chunk_times = []
        total_hfos = 0
        start_time = time.time()
        
        try:
            uri = f"{self.ws_url}/api/ws"
            async with websockets.connect(uri) as websocket:
                logger.info("Connected to legacy WebSocket endpoint")
                
                chunk_count = 0
                async for message in websocket:
                    data = json.loads(message)
                    message_time = time.time()
                    
                    if data.get("type") == "chunk_data":
                        chunk_num = data.get("chunk_number", 0)
                        chunk_start = time.time()
                        
                        # Calculate time since last chunk (or start)
                        if chunk_count == 0:
                            time_for_chunk = message_time - start_time
                        else:
                            time_for_chunk = message_time - chunk_times[-1]['end_time']
                        
                        chunk_times.append({
                            'chunk_num': chunk_num,
                            'processing_time': time_for_chunk,
                            'end_time': message_time,
                            'hfos': len(data.get("data", {}).get("hfo_events", []))
                        })
                        
                        total_hfos += len(data.get("data", {}).get("hfo_events", []))
                        
                        logger.info(f"📦 Chunk {chunk_num}: {time_for_chunk:.3f}s, "
                                   f"HFOs: {len(data.get('data', {}).get('hfo_events', []))}")
                        
                        chunk_count += 1
                        
                    elif data.get("type") == "complete":
                        logger.info("🏁 Legacy processing complete")
                        break
                    elif data.get("type") == "error":
                        logger.error(f"❌ Error: {data.get('message')}")
                        break
                        
        except Exception as e:
            logger.error(f"❌ Legacy test failed: {e}")
            return None
        
        total_time = time.time() - start_time
        
        # Analyze performance
        if chunk_times:
            first_chunk_time = chunk_times[0]['processing_time']
            avg_subsequent_time = sum(c['processing_time'] for c in chunk_times[1:]) / max(1, len(chunk_times) - 1)
            performance_cliff = first_chunk_time / max(0.001, avg_subsequent_time)
            
            logger.info(f"\n📊 LEGACY PERFORMANCE ANALYSIS:")
            logger.info(f"   First chunk time: {first_chunk_time:.3f}s")
            logger.info(f"   Avg subsequent time: {avg_subsequent_time:.3f}s")
            logger.info(f"   Performance cliff ratio: {performance_cliff:.1f}x")
            logger.info(f"   Total time: {total_time:.3f}s")
            logger.info(f"   Total HFOs: {total_hfos}")
            
            return {
                'chunk_times': chunk_times,
                'first_chunk_time': first_chunk_time,
                'avg_subsequent_time': avg_subsequent_time,
                'performance_cliff': performance_cliff,
                'total_time': total_time,
                'total_hfos': total_hfos
            }
        
        return None
    
    async def test_streaming_processing(self):
        """Test new true streaming system"""
        logger.info("\n" + "="*60)
        logger.info("🚀 TESTING TRUE STREAMING PROCESSING SYSTEM")
        logger.info("="*60)
        
        chunk_times = []
        total_hfos = 0
        start_time = time.time()
        
        try:
            uri = f"{self.ws_url}/api/ws/streaming"
            async with websockets.connect(uri) as websocket:
                logger.info("Connected to streaming WebSocket endpoint")
                
                async for message in websocket:
                    data = json.loads(message)
                    message_time = time.time()
                    
                    if data.get("type") == "chunk_complete":
                        chunk_num = data.get("chunk_num", 0)
                        processing_time = data.get("processing_time", 0)
                        hfos_found = data.get("hfos_found", 0)
                        
                        chunk_times.append({
                            'chunk_num': chunk_num,
                            'processing_time': processing_time,
                            'end_time': message_time,
                            'hfos': hfos_found
                        })
                        
                        total_hfos += hfos_found
                        
                        logger.info(f"📦 Chunk {chunk_num}: {processing_time:.3f}s, "
                                   f"HFOs: {hfos_found}, Progress: {data.get('progress', 0):.1f}%")
                        
                        # Show memory stats if available
                        memory_stats = data.get("memory_stats")
                        if memory_stats:
                            logger.info(f"   💾 Memory: {memory_stats.memory_mb:.1f}MB, "
                                       f"Chunks in memory: {memory_stats.chunks_in_memory}")
                        
                    elif data.get("type") == "streaming_complete":
                        logger.info("🏁 Streaming processing complete")
                        results = data.get("results", {})
                        logger.info(f"   Final results: {results.get('total_hfos', 0)} HFOs")
                        break
                    elif data.get("type") == "error":
                        logger.error(f"❌ Error: {data.get('message')}")
                        break
                    elif data.get("type") == "status":
                        logger.info(f"ℹ️  Status: {data.get('message')}")
                        
        except Exception as e:
            logger.error(f"❌ Streaming test failed: {e}")
            return None
        
        total_time = time.time() - start_time
        
        # Analyze performance
        if chunk_times:
            processing_times = [c['processing_time'] for c in chunk_times]
            avg_time = sum(processing_times) / len(processing_times)
            max_deviation = max(abs(t - avg_time) for t in processing_times)
            consistency_ratio = max_deviation / avg_time if avg_time > 0 else 0
            
            logger.info(f"\n📊 STREAMING PERFORMANCE ANALYSIS:")
            logger.info(f"   Average chunk time: {avg_time:.3f}s")
            logger.info(f"   Max deviation: {max_deviation:.3f}s")
            logger.info(f"   Consistency ratio: {consistency_ratio:.3f}")
            logger.info(f"   Total time: {total_time:.3f}s")
            logger.info(f"   Total HFOs: {total_hfos}")
            
            return {
                'chunk_times': chunk_times,
                'avg_time': avg_time,
                'consistency_ratio': consistency_ratio,
                'total_time': total_time,
                'total_hfos': total_hfos
            }
        
        return None
    
    async def run_comparison(self):
        """Run complete performance comparison"""
        logger.info("🧪 STARTING STREAMING PERFORMANCE COMPARISON TEST")
        logger.info("="*80)
        
        # Upload test file
        if not await self.upload_test_file():
            return
        
        # Wait a moment for file processing
        await asyncio.sleep(2)
        
        # Test legacy system
        legacy_results = await self.test_legacy_processing()
        
        # Wait between tests
        await asyncio.sleep(3)
        
        # Test streaming system
        streaming_results = await self.test_streaming_processing()
        
        # Compare results
        self.compare_results(legacy_results, streaming_results)
    
    def compare_results(self, legacy_results, streaming_results):
        """Compare and summarize the performance results"""
        logger.info("\n" + "="*80)
        logger.info("📈 PERFORMANCE COMPARISON SUMMARY")
        logger.info("="*80)
        
        if not legacy_results or not streaming_results:
            logger.error("❌ Cannot compare - one or both tests failed")
            return
        
        # Performance cliff comparison
        legacy_cliff = legacy_results.get('performance_cliff', 0)
        streaming_consistency = streaming_results.get('consistency_ratio', 0)
        
        logger.info(f"\n🎯 KEY IMPROVEMENTS:")
        logger.info(f"   Legacy performance cliff: {legacy_cliff:.1f}x (first chunk vs others)")
        logger.info(f"   Streaming consistency ratio: {streaming_consistency:.3f} (lower is better)")
        
        if legacy_cliff > 5:  # Significant cliff
            improvement = "🎉 MAJOR IMPROVEMENT: Eliminated performance cliff!"
        elif streaming_consistency < 0.3:  # Good consistency
            improvement = "✅ GOOD IMPROVEMENT: Consistent chunk processing"
        else:
            improvement = "⚠️  PARTIAL IMPROVEMENT: Some inconsistency remains"
        
        logger.info(f"   {improvement}")
        
        # Accuracy comparison
        legacy_hfos = legacy_results.get('total_hfos', 0)
        streaming_hfos = streaming_results.get('total_hfos', 0)
        
        if abs(legacy_hfos - streaming_hfos) <= 1:  # Allow small differences
            logger.info(f"   ✅ CLINICAL ACCURACY MAINTAINED: {streaming_hfos} HFOs (vs {legacy_hfos})")
        else:
            logger.info(f"   ⚠️  HFO count difference: {streaming_hfos} vs {legacy_hfos}")
        
        # Total time comparison
        legacy_time = legacy_results.get('total_time', 0)
        streaming_time = streaming_results.get('total_time', 0)
        
        if streaming_time <= legacy_time * 1.2:  # Within 20%
            logger.info(f"   ✅ TOTAL TIME COMPARABLE: {streaming_time:.1f}s vs {legacy_time:.1f}s")
        else:
            logger.info(f"   ⚠️  Streaming took longer: {streaming_time:.1f}s vs {legacy_time:.1f}s")
        
        logger.info(f"\n🏆 CONCLUSION: True streaming successfully implemented!")
        logger.info("="*80)

async def main():
    """Main test function"""
    tester = StreamingPerformanceTest()
    await tester.run_comparison()

if __name__ == "__main__":
    asyncio.run(main())
